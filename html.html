<html lang="en" style="--divider-color: rgba(0, 0, 0, 0.06); --background-color: #ECECEC; --selection-color: #f5f5f5; --scroller-color: #90939980; --primary-main: #007AFF; --background-color-alpha: rgba(0, 122, 255, 0.1); --window-border-color: #cccccc; --scrollbar-bg: #f1f1f1; --scrollbar-thumb: #c1c1c1; --user-background-image: none; --background-blend-mode: normal; --background-opacity: 1;"><head>
    <script type="module" crossorigin="" src="/assets/polyfills-CDJKeUtF.js"></script>

    <script>self["MonacoEnvironment"] = (function (paths) {
          return {
            globalAPI: false,
            getWorkerUrl : function (moduleId, label) {
              var result =  paths[label];
              if (/^((http:)|(https:)|(file:)|(\/\/))/.test(result)) {
                var currentUrl = String(window.location);
                var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);
                if (result.substring(0, currentOrigin.length) !== currentOrigin) {
                  var js = '/*' + label + '*/importScripts("' + result + '");';
                  var blob = new Blob([js], { type: 'application/javascript' });
                  return URL.createObjectURL(blob);
                }
              }
              return result;
            }
          };
        })({
  "editorWorkerService": "/monacoeditorwork/editor.worker.bundle.js",
  "typescript": "/monacoeditorwork/ts.worker.bundle.js",
  "css": "/monacoeditorwork/css.worker.bundle.js",
  "yaml": "/monacoeditorwork/yaml.worker.bundle.js",
  "javascript": "/monacoeditorwork/ts.worker.bundle.js",
  "less": "/monacoeditorwork/css.worker.bundle.js",
  "scss": "/monacoeditorwork/css.worker.bundle.js"
});</script>

    <meta charset="UTF-8">
    <link rel="shortcut icon" href="/assets/logo-yCaNqQic.ico" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clash Verge</title>
    <script type="module" crossorigin="" src="/assets/index-Ps--pJne.js"></script>
    <link rel="modulepreload" crossorigin="" href="/assets/small-vendors-CzWOEpqI.js">
    <link rel="modulepreload" crossorigin="" href="/assets/utils-BRZS8P_L.js">
    <link rel="modulepreload" crossorigin="" href="/assets/react-sTw6X_3n.js">
    <link rel="modulepreload" crossorigin="" href="/assets/tauri-plugins-B713VIUi.js">
    <link rel="modulepreload" crossorigin="" href="/assets/monaco-editor-D90QLlnq.js">
    <link rel="stylesheet" crossorigin="" href="/assets/react-BU3nLhQd.css">
    <link rel="stylesheet" crossorigin="" href="/assets/monaco-editor-DIPB3Vbg.css">
    <link rel="stylesheet" crossorigin="" href="/assets/index-BbAa9bmE.css">
  <style id="verge-theme">/*



*/
section {
  background-color: rgba(255, 255, 255, 0) !important;
}


html {
font-size: 22px;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
--background-color: #f0f0f000 !important;
}

.base-container {
  background-color: #f0f0f000 !important;
}


.MuiTypography-root {
  background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
  background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*浅色背景底色*/
.css-1li7dvq::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.css-l3ykv8 {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
  color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*深色背景底色*/
.css-l3ykv8::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: -1;
}


/* 主题模式和变量定义 */
:root {
/* 浅色模式变量 */

--border-light: rgba(0, 0, 0, 0.08);
--shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
--shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
--shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);


/* 布局变量 */
--base-spacing: 1.5rem;
--grid-gap: 1.2rem;
--card-min-width: 280px;
--sidebar-width: 280px;
--header-height: 64px;
--border-radius: 12px;
--border-radius-small: 8px;
--border-radius-large: 16px;
--nav-item-width: 220px;
--nav-item-border-radius: 30px;
--card-bg-light: rgba(255, 255, 255, 0.85);
--card-bg-dark: rgba(33, 33, 33, 0.85);
--card-bg-light-hover: rgba(255, 255, 255, 0.05);
--card-bg-dark-hover: rgba(33, 33, 33, 0.05);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
:root:not([data-theme="light"]):not(.light-mode) {

  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-inverse: rgba(33, 37, 41, 0.95);

  --border-light: rgba(255, 255, 255, 0.08);

  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.5);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);


}
}


/* 左侧导航栏 */
.layout__left{
flex: 0 0 var(--sidebar-width);
background: var(--background-glass);
backdrop-filter: var(--blur-medium);
border-right: 1px solid var(--border-light);
padding: var(--base-spacing);
min-width: 360px;
max-width: 420px;
box-shadow: var(--shadow-light);
position: relative;
z-index: 10;
}


/* ========================================
 响应式网格系统
 ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
gap: var(--grid-gap);
padding: var(--grid-gap);
width: 100%;
align-items: start;
}

/* 网格项目 */
.grid-item {
display: flex;
flex-direction: column;
min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--base-spacing);
margin: var(--base-spacing) 0;
}


/* ========================================
 Material-UI 组件优化
 ======================================== */

/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
border: 1px solid var(--border-light) !important;
backdrop-filter: var(--blur-light);
border-radius: var(--border-radius-small) !important;

box-shadow: var(--shadow-light) !important;
}

/* ========================================
 赛博朋克卡片悬停特效
 ======================================== */

/* 定义赛博朋克风格的颜色和动画变量 */
:root {
--cyber-c1: #00fffc; /* 亮青色 */
--cyber-c2: #ff00ff; /* 品红色 */
--cyber-c3: #faff00; /* 亮黄色 */
--cyber-border-angle: 0deg;
/* 革命性粒子颜色系统 - 100种颜色，11个核心粒子 */
--cyber-p1: #ff0000; --cyber-p2: #ff1a00; --cyber-p3: #ff3300; --cyber-p4: #ff4d00; --cyber-p5: #ff6600;
--cyber-p6: #ff8000; --cyber-p7: #ff9900; --cyber-p8: #ffb300; --cyber-p9: #ffcc00; --cyber-p10: #ffe600;
--cyber-p11: #ffff00; --cyber-p12: #e6ff00; --cyber-p13: #ccff00; --cyber-p14: #b3ff00; --cyber-p15: #99ff00;
--cyber-p16: #80ff00; --cyber-p17: #66ff00; --cyber-p18: #4dff00; --cyber-p19: #33ff00; --cyber-p20: #1aff00;
--cyber-p21: #00ff00; --cyber-p22: #00ff1a; --cyber-p23: #00ff33; --cyber-p24: #00ff4d; --cyber-p25: #00ff66;
--cyber-p26: #00ff80; --cyber-p27: #00ff99; --cyber-p28: #00ffb3; --cyber-p29: #00ffcc; --cyber-p30: #00ffe6;
--cyber-p31: #00ffff; --cyber-p32: #00e6ff; --cyber-p33: #00ccff; --cyber-p34: #00b3ff; --cyber-p35: #0099ff;
--cyber-p36: #0080ff; --cyber-p37: #0066ff; --cyber-p38: #004dff; --cyber-p39: #0033ff; --cyber-p40: #001aff;
--cyber-p41: #0000ff; --cyber-p42: #1a00ff; --cyber-p43: #3300ff; --cyber-p44: #4d00ff; --cyber-p45: #6600ff;
--cyber-p46: #8000ff; --cyber-p47: #9900ff; --cyber-p48: #b300ff; --cyber-p49: #cc00ff; --cyber-p50: #e600ff;
--cyber-p51: #ff00ff; --cyber-p52: #ff00e6; --cyber-p53: #ff00cc; --cyber-p54: #ff00b3; --cyber-p55: #ff0099;
--cyber-p56: #ff0080; --cyber-p57: #ff0066; --cyber-p58: #ff004d; --cyber-p59: #ff0033; --cyber-p60: #ff001a;
--cyber-p61: #ff4080; --cyber-p62: #ff8040; --cyber-p63: #ffbf40; --cyber-p64: #dfff40; --cyber-p65: #9fff40;
--cyber-p66: #60ff40; --cyber-p67: #40ff60; --cyber-p68: #40ff9f; --cyber-p69: #40ffdf; --cyber-p70: #40bfff;
--cyber-p71: #4080ff; --cyber-p72: #4040ff; --cyber-p73: #8040ff; --cyber-p74: #bf40ff; --cyber-p75: #ff40df;
--cyber-p76: #ff409f; --cyber-p77: #ff4060; --cyber-p78: #ff6040; --cyber-p79: #ff9f40; --cyber-p80: #ffdf40;
--cyber-p81: #dfff80; --cyber-p82: #9fff80; --cyber-p83: #60ff80; --cyber-p84: #40ff80; --cyber-p85: #40ff9f;
--cyber-p86: #40ffbf; --cyber-p87: #40ffdf; --cyber-p88: #40dfff; --cyber-p89: #40bfff; --cyber-p90: #409fff;
--cyber-p91: #4080ff; --cyber-p92: #4060ff; --cyber-p93: #6040ff; --cyber-p94: #8040ff; --cyber-p95: #9f40ff;
--cyber-p96: #bf40ff; --cyber-p97: #df40ff; --cyber-p98: #ff40df; --cyber-p99: #ff40bf; --cyber-p100: #ff409f;
/* 核心11个粒子选择 - 动态颜色轮换系统 */
--particle-1: var(--cyber-p5); --particle-2: var(--cyber-p15); --particle-3: var(--cyber-p25); --particle-4: var(--cyber-p35);
--particle-5: var(--cyber-p45); --particle-6: var(--cyber-p55); --particle-7: var(--cyber-p65); --particle-8: var(--cyber-p75);
--particle-9: var(--cyber-p85); --particle-10: var(--cyber-p95); --particle-11: var(--cyber-p50);
/* 动态颜色组 - 用于轮换效果 */
--color-group-1: var(--cyber-p1), var(--cyber-p21), var(--cyber-p41), var(--cyber-p61), var(--cyber-p81);
--color-group-2: var(--cyber-p10), var(--cyber-p30), var(--cyber-p50), var(--cyber-p70), var(--cyber-p90);
--color-group-3: var(--cyber-p5), var(--cyber-p25), var(--cyber-p45), var(--cyber-p65), var(--cyber-p85);
}

/* 注册 CSS 变量以实现平滑动画 */
@property --cyber-border-angle {
syntax: '<angle>';
inherits: false;
initial-value: 0deg;
}

/* 统一所有卡片和按钮的基础样式，为特效做准备 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.main .bg-primary-foreground, .side .bg-primary-foreground,
.main .bg-foreground, .side .bg-foreground,
.main .bg-content1, .side .bg-content1,
.main .bg-default, .side .bg-default,
.MuiButton-root, .MuiListItemButton-root {
transition: transform 0.3s ease, background-color 0.3s ease !important;
position: relative !important;
overflow: hidden !important; /* 包含内部特效 */
z-index: 1 !important;
}

/* 为特定卡片设置背景和圆角 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
background-color: var(--card-bg-light) !important;
border-radius: var(--border-radius) !important;
}

/* 深色模式下的卡片背景 */
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
  background-color: var(--card-bg-dark) !important;
}
}

/* 动态边框的容器 - 使用 ::before */
.MuiBox-root .css-1ow8u3y::before, .css-1rgmi2n::before, .css-bjjbb7::before, .css-hds0vx::before,
.css-aafiep::before, .css-xd8r7u::before, .css-ya2z3b::before, .css-8sla8j::before, .css-ulr2qx::before, .css-17rlh6j::before,
.main .bg-primary-foreground::before, .side .bg-primary-foreground::before,
.main .bg-foreground::before, .side .bg-foreground::before,
.main .bg-content1::before, .side .bg-content1::before,
.main .bg-default::before, .side .bg-default::before,
.MuiButton-root::before, .MuiListItemButton-root::before {
content: '' !important;
position: absolute !important;
top: 0; left: 0; right: 0; bottom: 0;
border-radius: inherit !important; /* 继承父元素的圆角 */
padding: 2px !important; /* 边框宽度 */
background: conic-gradient(from var(--cyber-border-angle), var(--cyber-c2), var(--cyber-c1), var(--cyber-c3), var(--cyber-c2)) !important;
-webkit-mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
-webkit-mask-composite: xor !important;
mask-composite: exclude !important;
animation: cyberpunk-border-flow 4s linear infinite !important;
opacity: 0 !important;
transition: opacity 0.4s ease-in-out !important;
z-index: -1 !important;
pointer-events: none !important;
}

/* 全新弧形轨迹粒子效果 - 360度绽放覆盖整个卡片 */
.MuiBox-root .css-1ow8u3y::after, .css-1rgmi2n::after, .css-bjjbb7::after, .css-hds0vx::after,
.css-aafiep::after, .css-xd8r7u::after, .css-ya2z3b::after, .css-8sla8j::after, .css-ulr2qx::after, .css-17rlh6j::after,
.main .bg-primary-foreground::after, .side .bg-primary-foreground::after,
.main .bg-foreground::after, .side .bg-foreground::after,
.main .bg-content1::after, .side .bg-content1::after,
.main .bg-default::after, .side .bg-default::after,
.MuiButton-root::after, .MuiListItemButton-root::after {
content: '' !important;
position: absolute !important;
top: 2px; left: 2px; right: 2px; bottom: 2px; /* 避免覆盖边框 */
border-radius: inherit !important;
overflow: hidden !important;
background-image:
  /* 使用扩展的粒子颜色变量 */
  radial-gradient(var(--particle-red) 1.2px, transparent 0),
  radial-gradient(var(--particle-orange) 0.8px, transparent 0),
  radial-gradient(var(--particle-yellow) 1.5px, transparent 0),
  radial-gradient(var(--particle-green) 1px, transparent 0),
  radial-gradient(var(--particle-cyan) 1.3px, transparent 0),
  radial-gradient(var(--particle-blue) 0.9px, transparent 0),
  radial-gradient(var(--particle-purple) 1.1px, transparent 0),
  radial-gradient(var(--particle-magenta) 1.4px, transparent 0),
  radial-gradient(var(--particle-neon-pink) 1.6px, transparent 0),
  radial-gradient(var(--particle-neon-green) 0.7px, transparent 0),
  radial-gradient(var(--particle-neon-blue) 1.8px, transparent 0),
  radial-gradient(var(--particle-gold) 1.3px, transparent 0),
  radial-gradient(var(--particle-silver) 0.9px, transparent 0),
  radial-gradient(var(--particle-ruby) 1.4px, transparent 0),
  radial-gradient(var(--particle-emerald) 0.8px, transparent 0) !important;

background-size:
  47px 47px, 67px 67px, 83px 83px, 59px 59px,
  101px 101px, 131px 131px, 157px 157px, 113px 113px,
  179px 179px, 191px 191px, 227px 227px, 239px 239px,
  97px 97px, 109px 109px, 127px 127px !important;

/* 恢复原有的动画系统，增强随机弧形轨迹 */
animation:
  cyber-particles-ultra-fast 12s linear infinite,
  cyber-particles-very-fast 18s ease-in-out infinite,
  cyber-particles-fast 24s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
  cyber-particles-medium-fast 32s ease-out infinite,
  cyber-particles-medium 42s ease-in infinite,
  cyber-particles-medium-slow 54s cubic-bezier(0.55, 0.06, 0.68, 0.19) infinite,
  cyber-particles-slow 68s ease-in-out infinite,
  cyber-particles-very-slow 78s linear infinite,
  cyber-particles-ultra-slow 88s cubic-bezier(0.17, 0.67, 0.83, 0.67) infinite,
  cyber-particles-color-shift 20s ease-in-out infinite,
  cyber-particles-size-pulse 16s cubic-bezier(0.4, 0, 0.6, 1) infinite,
  cyber-particles-opacity-wave 14s ease-in-out infinite !important;
opacity: 0 !important;
transition: opacity 0.5s ease-in-out !important;
z-index: 0 !important;
pointer-events: none !important;
}

/* 激活悬停效果 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover,
.main .bg-primary-foreground:hover, .side .bg-primary-foreground:hover,
.main .bg-foreground:hover, .side .bg-foreground:hover,
.main .bg-content1:hover, .side .bg-content1:hover,
.main .bg-default:hover, .side .bg-default:hover,
.MuiButton-root:hover, .MuiListItemButton-root:hover {
transform: translateY(-4px) !important;
}

/* 悬停时卡片背景透明度变化 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
background-color: var(--card-bg-light-hover) !important;
}
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
  background-color: var(--card-bg-dark-hover) !important;
}
}

/* 悬停时激活边框并启动透明度脉冲动画 */
.MuiBox-root .css-1ow8u3y:hover::before, .css-1rgmi2n:hover::before, .css-bjjbb7:hover::before, .css-hds0vx:hover::before,
.css-aafiep:hover::before, .css-xd8r7u:hover::before, .css-ya2z3b:hover::before, .css-8sla8j:hover::before, .css-ulr2qx:hover::before, .css-17rlh6j:hover::before,
.main .bg-primary-foreground:hover::before, .side .bg-primary-foreground:hover::before,
.main .bg-foreground:hover::before, .side .bg-foreground:hover::before,
.main .bg-content1:hover::before, .side .bg-content1:hover::before,
.main .bg-default:hover::before, .side .bg-default:hover::before,
.MuiButton-root:hover::before, .MuiListItemButton-root:hover::before {
opacity: 1 !important;
animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}

/* 悬停时激活粒子效果 */
.MuiBox-root .css-1ow8u3y:hover::after, .css-1rgmi2n:hover::after, .css-bjjbb7:hover::after, .css-hds0vx:hover::after,
.css-aafiep:hover::after, .css-xd8r7u:hover::after, .css-ya2z3b:hover::after, .css-8sla8j:hover::after, .css-ulr2qx:hover::after, .css-17rlh6j:hover::after,
.main .bg-primary-foreground:hover::after, .side .bg-primary-foreground:hover::after,
.main .bg-foreground:hover::after, .side .bg-foreground:hover::after,
.main .bg-content1:hover::after, .side .bg-content1:hover::after,
.main .bg-default:hover::after, .side .bg-default:hover::after,
.MuiButton-root:hover::after, .MuiListItemButton-root:hover::after {
opacity: 1 !important;
}


/* 导航项目整体盒子*/
.css-dh9epo{
min-width:280px;
}

/* 导航项目样式优化 */
.MuiListItem-root .MuiListItemButton-root {
width: var(--nav-item-width) !important;
border-radius: var(--nav-item-border-radius) !important;
background-color: transparent !important;
margin: 4px 0 !important;
transition: all 0.3s ease !important;
display: flex !important;
align-items: center !important;
justify-content: center !important;
}

/* 导航项目文字水平居中 */
.MuiListItem-root .MuiListItemButton-root .MuiListItemText-root {
display: flex !important;
align-items: center !important;
}

/* ========================================
 Gemini 风格渐变色和悬停效果
 ======================================== */

/* Gemini 风格 CSS 变量定义 */
:root {
--gemini-color-logo-gradient: linear-gradient(90deg,
    #2079fe 0%,
    #098efb 33.53%,
    #ad89eb 70%,
    #ef4e5e 100%);
--gemini-color-white: #ffffff;
--gemini-color-black: #000000;
--gemini-color-grey-50: #f8f9fa;
--gemini-color-grey-100: #f1f3f4;
--gemini-color-grey-200: #e8eaed;
--gemini-color-grey-300: #dadce0;
--gemini-color-grey-400: #bdc1c6;
--gemini-color-grey-500: #9aa0a6;
--gemini-color-grey-600: #80868b;
--gemini-color-grey-700: #5f6368;
--gemini-color-grey-800: #3c4043;
--gemini-color-grey-900: #202124;
--gemini-color-blue-50: #e8f0fe;
--gemini-color-blue-100: #d2e3fc;
--gemini-color-blue-200: #aecbfa;
--gemini-color-blue-300: #8ab4f8;
--gemini-color-blue-400: #669df6;
--gemini-color-blue-500: #4285f4;
--gemini-color-blue-600: #1a73e8;
--gemini-color-blue-700: #1967d2;
--gemini-color-blue-800: #185abc;
--gemini-color-blue-900: #174ea6;
--gemini-color-red-50: #fce8e6;
--gemini-color-red-100: #fad2cf;
--gemini-color-red-200: #f6aea9;
--gemini-color-red-300: #f28b82;
--gemini-color-red-400: #ee675c;
--gemini-color-red-500: #ea4335;
--gemini-color-red-600: #d93025;
--gemini-color-red-700: #c5221f;
--gemini-color-red-800: #b31412;
--gemini-color-red-900: #a50e0e;
--gemini-color-green-50: #e6f4ea;
--gemini-color-green-100: #ceead6;
--gemini-color-green-200: #a8dab5;
--gemini-color-green-300: #81c995;
--gemini-color-green-400: #5bb974;
--gemini-color-green-500: #34a853;
--gemini-color-green-600: #137333;
--gemini-color-green-700: #0d652d;
--gemini-color-green-800: #0b5394;
--gemini-color-green-900: #0a5d00;
--gemini-color-yellow-50: #fef7e0;
--gemini-color-yellow-100: #feefc3;
--gemini-color-yellow-200: #fde047;
--gemini-color-yellow-300: #fcd34d;
--gemini-color-yellow-400: #fbbf24;
--gemini-color-yellow-500: #f59e0b;
--gemini-color-yellow-600: #d97706;
--gemini-color-yellow-700: #b45309;
--gemini-color-yellow-800: #92400e;
--gemini-color-yellow-900: #78350f;
--gemini-color-gemini-blue: #368efe;
--gemini-color-gemini-cyan: #4fabff;
--gemini-color-gemini-light-blue: #b1c5ff;
--gemini-color-blue: #368efe;
--gemini-color-purple-100: #ac87eb;
--gemini-color-red-200: #ee4d5d;
--gemini-color-green-800: #137333;
--gemini-color-blue-800: #185ABC;
--gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
--gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
--gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 70%, #ee4d5d 100%);
--gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
--gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
--gemini-color-foreground: var(--gemini-color-white);
--gemini-color-background: var(--gemini-color-grey-900);
--gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
--gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
--gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
--gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
--gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
--gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

/* 平滑渐变色彩 - 更自然的过渡 */
--smooth-gradient: linear-gradient(90deg,
  #669df6 0%,   /* Blue */
  #7e57c2 25%,  /* Indigo */
  #ab47bc 50%,  /* Purple */
  #ec407a 75%,  /* Pink */
  #ef5350 100%  /* Red */
);

/* 平滑流动渐变 - 更长的过渡距离 */
--smooth-flowing-gradient: linear-gradient(90deg,
  #669df6, #7e57c2, #ab47bc, #ec407a, #ef5350, #ec407a, #ab47bc, #7e57c2, #669df6
);
}


/* 左侧导航文字渐变效果 - 使用平滑渐变 */
.MuiListItemText-primary {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
left: 50px !important;
width: 100% !important;
}


/* 文字渐变流动动画 */
@keyframes text-gradient-flow {
0% {
  background-position: 0% center !important;
}
100% {
  background-position: 200% center !important;
}
}

/* SVG logo震撼渐变效果 - 多彩流动渐变 */
#layout1 {
position: relative !important;
overflow: hidden !important;
}

/* 为SVG添加动态渐变背景 */
#layout1::before {
content: '' !important;
position: absolute !important;
top: -50% !important;
left: -50% !important;
width: 200% !important;
height: 200% !important;
background: linear-gradient(
  45deg,
  #4285f4 0%,
  #ea4335 15%,
  #fbbc04 30%,
  #34a853 45%,
  #4285f4 60%,
  #ea4335 75%,
  #fbbc04 90%,
  #34a853 100%
) !important;
background-size: 400% 400% !important;
animation: logo-gradient-flow 6s ease-in-out infinite !important;
z-index: -1 !important;
border-radius: 50% !important;
filter: blur(8px) !important;
}

/* SVG路径的震撼效果 */
#layout1 .st1 {
fill: #e22acd !important;

}

/* 震撼的渐变流动动画 */
@keyframes logo-gradient-flow {
0% {
  background-position: 0% 50% !important;
  transform: rotate(0deg) scale(1) !important;
}
25% {
  background-position: 100% 50% !important;
  transform: rotate(90deg) scale(1.1) !important;
}
50% {
  background-position: 100% 100% !important;
  transform: rotate(180deg) scale(1.2) !important;
}
75% {
  background-position: 0% 100% !important;
  transform: rotate(270deg) scale(1.1) !important;
}
100% {
  background-position: 0% 50% !important;
  transform: rotate(360deg) scale(1) !important;
}
}

/* 脉冲效果 */
@keyframes logo-pulse {
0% {
  filter: drop-shadow(0 0 8px rgba(66, 133, 244, 0.6))
          drop-shadow(0 0 16px rgba(234, 67, 53, 0.4))
          drop-shadow(0 0 24px rgba(251, 188, 4, 0.3)) !important;
}
100% {
  filter: drop-shadow(0 0 12px rgba(66, 133, 244, 0.8))
          drop-shadow(0 0 24px rgba(234, 67, 53, 0.6))
          drop-shadow(0 0 36px rgba(251, 188, 4, 0.5)) !important;
}
}

/* 颜色变换动画 */
@keyframes logo-color-shift {
0% { fill: #4285f4 !important; }
25% { fill: #ea4335 !important; }
50% { fill: #fbbc04 !important; }
75% { fill: #34a853 !important; }
100% { fill: #4285f4 !important; }
}

/* data-tauri-drag-region 元素文字渐变效果 */
.css-1l0zim6 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
}

/* 右侧设置页面文字渐变效果 - 只针对文字，不影响图标 */
.css-1i24pk4 span:not([class*="MuiSvgIcon"]):not([class*="Icon"]) {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
font-size: 20px;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
}

/* 赛博朋克边框旋转动画 */
@keyframes cyberpunk-border-flow {
to {
  --cyber-border-angle: 360deg;
}
}

/* 新增：赛博朋克边框透明度脉冲动画 */
@keyframes cyberpunk-border-opacity-pulse {
0%, 100% { opacity: 0.6; }
50% { opacity: 1; }
}

/* 恢复并增强的粒子动画系统 - 随机弧形轨迹，360度任意方向 */

/* 扩展粒子颜色变量 */
:root {
  /* 彩虹光谱系列 */
  --particle-red: rgba(255, 0, 0, 0.9);
  --particle-orange: rgba(255, 140, 0, 0.9);
  --particle-yellow: rgba(255, 255, 0, 0.9);
  --particle-lime: rgba(50, 205, 50, 0.9);
  --particle-green: rgba(0, 255, 0, 0.9);
  --particle-cyan: rgba(0, 255, 255, 0.9);
  --particle-blue: rgba(0, 100, 255, 0.9);
  --particle-purple: rgba(138, 43, 226, 0.9);
  --particle-magenta: rgba(255, 0, 255, 0.9);

  /* 霓虹色系列 - 超鲜艳 */
  --particle-neon-pink: rgba(255, 20, 147, 1.0);
  --particle-neon-green: rgba(57, 255, 20, 1.0);
  --particle-neon-blue: rgba(0, 191, 255, 1.0);
  --particle-neon-orange: rgba(255, 69, 0, 1.0);
  --particle-neon-purple: rgba(148, 0, 211, 1.0);
  --particle-neon-yellow: rgba(255, 255, 0, 1.0);
  --particle-neon-red: rgba(255, 0, 54, 1.0);
  --particle-neon-cyan: rgba(0, 255, 255, 1.0);

  /* 鲜艳金属色 */
  --particle-gold: rgba(255, 215, 0, 0.95);
  --particle-silver: rgba(220, 220, 220, 0.95);
  --particle-copper: rgba(255, 140, 0, 0.95);
  --particle-bronze: rgba(205, 127, 50, 0.95);

  /* 宝石色系列 - 高亮版 */
  --particle-ruby: rgba(255, 0, 100, 0.95);
  --particle-emerald: rgba(0, 255, 100, 0.95);
  --particle-sapphire: rgba(0, 100, 255, 0.95);
  --particle-diamond: rgba(200, 255, 255, 0.95);
  --particle-amethyst: rgba(200, 100, 255, 0.95);

  /* 特殊鲜艳色 */
  --particle-electric: rgba(0, 255, 255, 1.0);
  --particle-fire: rgba(255, 69, 0, 1.0);
  --particle-plasma: rgba(255, 100, 255, 1.0);
  --particle-laser: rgba(255, 0, 255, 1.0);
  --particle-atomic: rgba(0, 255, 0, 1.0);
  --particle-pink: rgba(255, 192, 203, 0.9);
  --particle-teal: rgba(0, 128, 128, 0.9);
  --particle-violet: rgba(238, 130, 238, 0.9);
  --particle-brown: rgba(165, 42, 42, 0.9);
  --particle-indigo: rgba(75, 0, 130, 0.9);
  --particle-maroon: rgba(128, 0, 0, 0.9);
  --particle-navy: rgba(0, 0, 128, 0.9);
  --particle-olive: rgba(128, 128, 0, 0.9);
  --particle-coral: rgba(255, 127, 80, 0.9);
  --particle-crimson: rgba(220, 20, 60, 0.9);

  /* 统一粒子大小 */
  --particle-size: 2px;
}

/* 超快速弧形粒子动画 - 随机360度方向 */
@keyframes cyber-particles-ultra-fast {
0% { background-position: 13px 7px, 89px 23px, 156px 41px, 234px 67px, 312px 89px, 67px 134px, 145px 178px, 223px 201px, 301px 245px, 89px 289px, 167px 312px, 245px 356px, 323px 389px, 401px 423px, 123px 456px; }
25% { background-position: -234px 456px, 567px -123px, -345px 678px, 789px -234px, -456px 567px, 678px -345px, -567px 789px, 890px -456px, -678px 567px, 789px -678px, -890px 456px, 567px -789px, -456px 890px, 678px -567px, -789px 456px; }
50% { background-position: 678px -890px, -567px 789px, 890px -456px, -678px 567px, 789px -890px, -456px 678px, 567px -789px, -890px 456px, 678px -567px, -789px 890px, 456px -678px, -567px 789px, 890px -456px, -678px 567px, 789px -890px; }
75% { background-position: -890px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px, 678px 567px, -789px -890px, 456px 678px, -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px; }
100% { background-position: -1234px 1567px, -1789px -1234px, 1567px 1890px, 1234px -1567px, -1890px 1234px, 1567px -1789px, -1234px 1890px, 1789px -1234px, -1567px 1234px, 1890px -1567px, -1234px 1789px, 1567px -1890px, -1789px 1234px, 1234px -1567px, -1890px 1789px; }
}

/* 很快速弧形粒子动画 */
@keyframes cyber-particles-very-fast {
0% { background-position: 29px 13px, 107px 37px, 185px 61px, 263px 85px, 341px 109px, 85px 153px, 163px 177px, 241px 201px, 319px 225px, 107px 269px, 185px 293px, 263px 317px, 341px 361px, 419px 385px, 141px 429px; }
25% { background-position: -345px 567px, 678px -234px, -456px 789px, 567px -345px, -678px 456px, 789px -567px, -456px 678px, 567px -789px, -890px 456px, 678px -567px, -789px 890px, 456px -678px, -567px 789px, 890px -456px, -678px 567px; }
50% { background-position: 789px -456px, -678px 567px, 890px -789px, -456px 678px, 567px -890px, -789px 456px, 678px -567px, -890px 789px, 456px -678px, -567px 890px, 789px -456px, -678px 567px, 890px -789px, -456px 678px, 567px -890px; }
75% { background-position: -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px, 678px 567px, -789px -890px, 456px 678px, -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px; }
100% { background-position: -1456px 1789px, -1567px -1456px, 1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px; }
}

/* 快速弧形粒子动画 */
@keyframes cyber-particles-fast {
0% { background-position: 47px 19px, 125px 43px, 203px 67px, 281px 91px, 359px 115px, 103px 159px, 181px 183px, 259px 207px, 337px 231px, 125px 275px, 203px 299px, 281px 323px, 359px 347px, 437px 371px, 159px 415px; }
25% { background-position: -456px 678px, 789px -345px, -567px 890px, 678px -456px, -789px 567px, 890px -678px, -567px 789px, 678px -890px, -456px 567px, 789px -678px, -890px 456px, 567px -789px, -678px 890px, 456px -567px, -789px 678px; }
50% { background-position: 890px -567px, -789px 678px, 567px -890px, -678px 789px, 456px -567px, -890px 678px, 789px -456px, -567px 890px, 678px -789px, -456px 567px, 890px -678px, -789px 456px, 567px -890px, -678px 789px, 456px -567px; }
75% { background-position: -678px -890px, 567px 789px, -456px -567px, 890px 678px, -789px -456px, 567px 890px, -678px -789px, 456px 567px, -890px -678px, 789px 456px, -567px -890px, 678px 789px, -456px -567px, 890px 678px, -789px -456px; }
100% { background-position: -1678px 1456px, -1890px -1678px, 1456px 1890px, 1678px -1456px, -1890px 1678px, 1456px -1890px, -1678px 1456px, 1890px -1678px, -1456px 1890px, 1678px -1456px, -1890px 1678px, 1456px -1890px, -1678px 1456px, 1890px -1678px, -1456px 1890px; }
}

/* 中快速弧形粒子动画 */
@keyframes cyber-particles-medium-fast {
0% { background-position: 61px 31px, 139px 55px, 217px 79px, 295px 103px, 373px 127px, 117px 171px, 195px 195px, 273px 219px, 351px 243px, 139px 287px, 217px 311px, 295px 335px, 373px 359px, 451px 383px, 173px 427px; }
25% { background-position: -567px 789px, 890px -456px, -678px 567px, 789px -567px, -890px 678px, 567px -789px, -678px 890px, 789px -567px, -456px 678px, 890px -789px, -567px 456px, 678px -890px, -789px 567px, 456px -678px, -890px 789px; }
50% { background-position: 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px; }
75% { background-position: -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px; }
100% { background-position: -1789px 1567px, -1456px -1789px, 1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px; }
}

/* 中速弧形粒子动画 */
@keyframes cyber-particles-medium {
0% { background-position: 73px 41px, 151px 65px, 229px 89px, 307px 113px, 385px 137px, 129px 181px, 207px 205px, 285px 229px, 363px 253px, 151px 297px, 229px 321px, 307px 345px, 385px 369px, 463px 393px, 185px 437px; }
25% { background-position: -678px 890px, 567px -789px, -456px 678px, 890px -678px, -789px 567px, 678px -890px, -567px 789px, 890px -678px, -456px 567px, 789px -890px, -678px 456px, 567px -789px, -890px 678px, 789px -567px, -456px 890px; }
50% { background-position: 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px; }
75% { background-position: -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px; }
100% { background-position: -1567px 1890px, -1678px -1567px, 1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px; }
}

/* 中慢速弧形粒子动画 */
@keyframes cyber-particles-medium-slow {
0% { background-position: 97px 53px, 175px 77px, 253px 101px, 331px 125px, 409px 149px, 153px 193px, 231px 217px, 309px 241px, 387px 265px, 175px 309px, 253px 333px, 331px 357px, 409px 381px, 487px 405px, 209px 449px; }
25% { background-position: -789px 567px, 678px -890px, -567px 789px, 890px -789px, -678px 567px, 789px -567px, -890px 678px, 567px -789px, -678px 890px, 789px -567px, -456px 678px, 890px -789px, -567px 456px, 678px -890px, -789px 567px; }
50% { background-position: 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px; }
75% { background-position: -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px; }
100% { background-position: -1890px 1678px, -1567px -1890px, 1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px; }
}

/* 慢速弧形粒子动画 */
@keyframes cyber-particles-slow {
0% { background-position: 109px 67px, 187px 91px, 265px 115px, 343px 139px, 421px 163px, 165px 207px, 243px 231px, 321px 255px, 399px 279px, 187px 323px, 265px 347px, 343px 371px, 421px 395px, 499px 419px, 221px 463px; }
25% { background-position: -456px 789px, 890px -567px, -678px 456px, 567px -890px, -789px 678px, 456px -567px, -890px 789px, 678px -456px, -567px 890px, 789px -678px, -456px 567px, 890px -789px, -678px 456px, 567px -890px, -789px 678px; }
50% { background-position: 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px; }
75% { background-position: -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px; }
100% { background-position: -1456px 1789px, -1890px -1456px, 1789px 1890px, 1456px -1789px, -1890px 1456px, 1789px -1890px, -1456px 1789px, 1890px -1456px, -1789px 1890px, 1456px -1789px, -1890px 1456px, 1789px -1890px, -1456px 1789px, 1890px -1456px, -1789px 1890px; }
}

/* 很慢速弧形粒子动画 */
@keyframes cyber-particles-very-slow {
0% { background-position: 127px 79px, 205px 103px, 283px 127px, 361px 151px, 439px 175px, 183px 219px, 261px 243px, 339px 267px, 417px 291px, 205px 335px, 283px 359px, 361px 383px, 439px 407px, 517px 431px, 239px 475px; }
25% { background-position: -567px 456px, 678px -789px, -890px 567px, 456px -678px, -789px 890px, 567px -456px, -678px 789px, 890px -567px, -456px 678px, 789px -890px, -567px 456px, 678px -789px, -890px 567px, 456px -678px, -789px 890px; }
50% { background-position: 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px; }
75% { background-position: -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px; }
100% { background-position: -1678px 1567px, -1456px -1678px, 1567px 1456px, 1678px -1567px, -1456px 1678px, 1567px -1456px, -1678px 1567px, 1456px -1678px, -1567px 1456px, 1678px -1567px, -1456px 1678px, 1567px -1456px, -1678px 1567px, 1456px -1678px, -1567px 1456px; }
}

/* 超慢速弧形粒子动画 */
@keyframes cyber-particles-ultra-slow {
0% { background-position: 143px 97px, 221px 121px, 299px 145px, 377px 169px, 455px 193px, 199px 237px, 277px 261px, 355px 285px, 433px 309px, 221px 353px, 299px 377px, 377px 401px, 455px 425px, 533px 449px, 255px 493px; }
25% { background-position: -678px 345px, 456px -567px, -789px 678px, 345px -456px, -567px 789px, 678px -345px, -456px 567px, 789px -678px, -345px 456px, 567px -789px, -678px 345px, 456px -567px, -789px 678px, 345px -456px, -567px 789px; }
50% { background-position: 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px; }
75% { background-position: -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px; }
100% { background-position: -1345px 1678px, -1567px -1345px, 1678px 1567px, 1345px -1678px, -1567px 1345px, 1678px -1567px, -1345px 1678px, 1567px -1345px, -1678px 1567px, 1345px -1678px, -1567px 1345px, 1678px -1567px, -1345px 1678px, 1567px -1345px, -1678px 1567px; }
}

/* 颜色变化动画 */
@keyframes cyber-particles-color-shift {
0% { filter: hue-rotate(0deg) saturate(1) brightness(1); }
25% { filter: hue-rotate(90deg) saturate(1.2) brightness(1.1); }
50% { filter: hue-rotate(180deg) saturate(0.8) brightness(0.9); }
75% { filter: hue-rotate(270deg) saturate(1.3) brightness(1.2); }
100% { filter: hue-rotate(360deg) saturate(1) brightness(1); }
}

/* 大小脉冲动画 */
@keyframes cyber-particles-size-pulse {
0% { transform: scale(1) rotate(0deg); }
20% { transform: scale(1.1) rotate(72deg); }
40% { transform: scale(0.9) rotate(144deg); }
60% { transform: scale(1.2) rotate(216deg); }
80% { transform: scale(0.8) rotate(288deg); }
100% { transform: scale(1) rotate(360deg); }
}

/* 透明度波浪动画 */
@keyframes cyber-particles-opacity-wave {
0% { opacity: 0.3; }
15% { opacity: 0.7; }
30% { opacity: 0.4; }
45% { opacity: 0.8; }
60% { opacity: 0.5; }
75% { opacity: 0.9; }
90% { opacity: 0.6; }
100% { opacity: 0.3; }
}

h6 {
font-weight: bold !important;
background: linear-gradient(90deg,
  #ff0000 0%,   /* 红 */
  #ff3300 8%,   /* 红橙 */
  #ff6600 16%,  /* 橙 */
  #ff9900 24%,  /* 深橙 */
  #ffcc00 32%,  /* 金橙 */
  #ffff00 40%,  /* 黄 */
  #ccff00 48%,  /* 黄绿 */
  #66ff00 56%,  /* 绿黄 */
  #00ff00 64%,  /* 绿 */
  #00ff66 72%,  /* 绿青 */
  #00ffcc 80%,  /* 青绿 */
  #00ccff 88%,  /* 青 */
  #0066ff 96%,  /* 蓝 */
  #ff0000 100%  /* 回到红色形成循环 */
) !important;
background-size: 400% auto !important;
-webkit-background-clip: text !important;
background-clip: text !important;
-webkit-text-fill-color: transparent !important;
animation: rainbow-flow 8s ease-in-out infinite !important;
position: relative !important;
z-index: 1 !important;
/* 恢复文字选择功能 */
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

/* 修复h6和其他文字选择问题 */
h6::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

h6::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

/* 修复 .css-1u0w142 文字选择问题 */
.css-1u0w142 {
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

.css-1u0w142::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

.css-1u0w142::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}



@keyframes rainbow-flow {
0% {
  background-position: 0% center;
  filter: brightness(1) saturate(1);
}
25% {
  background-position: 100% center;
  filter: brightness(1.1) saturate(1.2);
}
50% {
  background-position: 200% center;
  filter: brightness(0.9) saturate(0.8);
}
75% {
  background-position: 300% center;
  filter: brightness(1.2) saturate(1.3);
}
100% {
  background-position: 400% center;
  filter: brightness(1) saturate(1);
}
}
        /* 修复滚动条样式 */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
          background-color: var(--scrollbar-bg);
        }
        ::-webkit-scrollbar-thumb {
          background-color: var(--scrollbar-thumb);
          border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background-color: #a1a1a1;
        }

        /* 背景图处理 */
        body {
          background-color: var(--background-color);
          
        }

        /* 修复可能的白色边框 */
        .MuiPaper-root {
          border-color: var(--window-border-color) !important;
        }

        /* 确保模态框和对话框也使用暗色主题 */
        .MuiDialog-paper {
          background-color: #ffffff !important;
        }

        /* 移除可能的白色点或线条 */
        * {
          outline: none !important;
          box-shadow: none !important;
        }
      </style><style data-emotion="css-global" data-s=""></style><style data-emotion="css-global" data-s=""></style><style data-emotion="css" data-s=""></style><style type="text/css" data-rbd-always="0"> [data-rbd-drag-handle-context-id="0"] { 
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
         } [data-rbd-droppable-context-id="0"] { overflow-anchor: none; } </style><style type="text/css" data-rbd-dynamic="0"> [data-rbd-drag-handle-context-id="0"] { 
      cursor: -webkit-grab;
      cursor: grab;
     }  </style></head>
  <body>
    <div id="root"><div class="MuiBox-root css-gu3gue"></div><div style="animation: 0.5s ease 0s 1 normal none running fadeIn;"></div><style>
            @keyframes fadeIn {
              from { opacity: 0; }
              to { opacity: 1; }
            }
          </style><div class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation0 windows layout css-1li7dvq" style="--Paper-shadow: none; border-top-left-radius: 0px; border-top-right-radius: 0px;"><div class="layout__left"><div class="the-logo" data-tauri-drag-region="true"><div data-tauri-drag-region="true" style="height: 27px; display: flex; justify-content: space-between;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0.00 0.00 512.00 512.00" width="512" height="512" class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" style="height: 36px; width: 36px; margin-top: -3px; margin-right: 5px; margin-left: -3px;"><path fill="#000000" d="
  M 66.75 243.26
  C 64.36 202.61 63.47 160.98 66.14 119.90
  Q 67.07 105.54 69.56 90.86
  C 71.35 80.37 74.26 67.20 81.13 59.68
  C 88.86 51.20 102.34 59.42 109.45 64.46
  Q 122.61 73.79 137.56 88.26
  Q 154.16 104.32 170.37 120.15
  Q 177.39 127.01 185.78 133.69
  C 189.58 136.71 194.75 140.48 199.81 139.03
  Q 256.12 122.89 312.63 139.17
  C 317.17 140.47 322.43 136.89 326.29 133.81
  Q 334.64 127.18 341.86 120.15
  Q 358.44 104.02 373.87 89.06
  Q 389.67 73.75 403.99 63.72
  Q 409.86 59.61 416.68 57.20
  C 430.17 52.45 435.71 64.65 438.76 74.78
  Q 442.82 88.24 444.57 104.64
  Q 447.71 133.95 447.66 168.99
  Q 447.61 205.59 445.24 243.61
  Q 445.21 244.12 445.44 244.57
  Q 459.30 271.43 466.56 302.09
  C 469.00 312.41 465.64 324.20 461.06 333.82
  C 449.65 357.80 430.14 378.99 409.62 396.13
  Q 372.77 426.90 329.61 443.00
  Q 266.07 466.70 201.80 449.27
  C 162.55 438.62 125.61 417.06 95.28 389.88
  C 77.45 373.90 60.60 354.63 50.57 332.92
  C 46.30 323.66 43.03 312.16 45.33 302.37
  Q 52.57 271.58 66.46 244.63
  Q 66.80 243.98 66.75 243.26
  Z
  M 129.31 310.72
  Q 136.38 328.58 152.74 336.68
  C 165.31 342.91 181.44 345.53 194.60 340.75
  C 211.72 334.54 209.96 316.29 200.74 304.29
  C 190.53 291.00 173.63 283.30 157.10 280.73
  C 136.41 277.52 120.03 287.25 129.31 310.72
  Z
  M 304.10 309.36
  C 297.35 321.61 299.56 335.79 313.93 340.88
  C 326.42 345.31 342.09 343.01 354.08 337.35
  Q 374.66 327.63 380.68 304.95
  C 386.50 282.97 365.69 278.03 349.30 281.14
  C 331.39 284.54 312.80 293.56 304.10 309.36
  Z
  M 244.39 396.99
  Q 252.76 401.23 260.59 398.28
  Q 271.64 394.13 281.68 382.89
  C 290.72 372.77 280.23 368.82 272.04 367.56
  Q 253.06 364.63 234.76 367.80
  C 228.71 368.85 218.66 372.23 224.67 380.57
  Q 231.98 390.72 244.39 396.99
  Z"></path></svg><svg id="layout1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 117 27" xml:space="preserve" fill="black"><g><defs><rect id="SVGID_1_" x="-39.9" width="157" height="27"></rect></defs><clipPath id="SVGID_00000023248255305809236420000007367745325967865768_"><use xlink:href="#SVGID_1_" style="overflow: visible;"></use></clipPath><g style="clip-path: url(&quot;#SVGID_00000023248255305809236420000007367745325967865768_&quot;);"><path class="st1" d="M115.9,21.4c-0.5,0.3-1.1,0.5-1.8,0.7c-0.7,0.1-1.3,0.2-1.9,0.2c-2.1,0-3.8-0.5-4.9-1.5 c-1.1-1-1.6-2.4-1.6-4.3c0-1.8,0.5-3.2,1.5-4.2c1-1,2.3-1.5,4-1.5c1.7,0,3,0.5,4,1.5c1,1,1.5,2.3,1.5,4.2c0,0.2,0,0.5,0,0.9h-7.8 c0.3,1.7,1.4,2.6,3.4,2.6c1.4,0,2.6-0.4,3.7-1.2V21.4z M113.6,15.2c-0.2-0.7-0.5-1.2-0.9-1.5c-0.4-0.3-0.9-0.5-1.5-0.5 c-0.6,0-1,0.2-1.4,0.5c-0.4,0.3-0.7,0.8-0.8,1.5H113.6z"></path><path class="st1" d="M98.5,26.6c-0.8,0-1.6-0.1-2.5-0.2c-0.8-0.1-1.5-0.3-2.2-0.5v-2.6c1.4,0.3,2.9,0.5,4.3,0.5 c0.9,0,1.6-0.2,2.1-0.6c0.5-0.4,0.7-1,0.7-1.7c-0.7,0.5-1.6,0.7-2.6,0.7c-1,0-1.9-0.2-2.6-0.7c-0.7-0.5-1.3-1.1-1.7-2 c-0.4-0.9-0.6-1.8-0.6-2.9c0-1.1,0.2-2.1,0.6-2.9c0.4-0.9,1-1.5,1.7-2c0.7-0.5,1.6-0.7,2.6-0.7c0.9,0,1.8,0.3,2.6,0.9v-0.7h3.1V22 C104,25,102.2,26.6,98.5,26.6z M96.4,16.6c0,0.6,0.1,1.2,0.4,1.7c0.3,0.5,0.6,0.9,1,1.2c0.4,0.3,0.8,0.4,1.3,0.4 c0.3,0,0.7-0.1,1.1-0.2c0.4-0.2,0.8-0.5,1.1-1l0.1-0.4v-3.7c-0.3-0.6-0.6-0.9-1.1-1.1c-0.4-0.2-0.8-0.3-1.2-0.3 c-0.5,0-0.9,0.1-1.3,0.4c-0.4,0.3-0.7,0.7-1,1.2C96.6,15.4,96.4,16,96.4,16.6z"></path><path class="st1" d="M89.2,11.2v1.2c0.3-0.4,0.8-0.7,1.2-0.9c0.5-0.2,1-0.3,1.5-0.3c0.3,0,0.6,0,0.9,0.1v2.5 c-0.4-0.1-0.7-0.1-1.1-0.1c-0.5,0-1,0.1-1.4,0.3c-0.5,0.2-0.8,0.4-1.1,0.8V22H86V11.2H89.2z"></path><path class="st1" d="M83.7,21.4c-0.5,0.3-1.1,0.5-1.8,0.7c-0.7,0.1-1.3,0.2-1.9,0.2c-2.1,0-3.8-0.5-4.9-1.5 c-1.1-1-1.6-2.4-1.6-4.3c0-1.8,0.5-3.2,1.5-4.2c1-1,2.3-1.5,4-1.5c1.7,0,3,0.5,4,1.5c1,1,1.5,2.3,1.5,4.2c0,0.2,0,0.5,0,0.9h-7.8 C76.9,19.1,78,20,80,20c1.4,0,2.6-0.4,3.7-1.2V21.4z M81.4,15.2c-0.2-0.7-0.5-1.2-0.9-1.5c-0.4-0.3-0.9-0.5-1.5-0.5 c-0.6,0-1,0.2-1.4,0.5c-0.4,0.3-0.7,0.8-0.8,1.5H81.4z"></path><path class="st1" d="M59.5,8h3.6l3.4,11.8h0.1L69.9,8h3.6l-4.3,14h-5.3L59.5,8z"></path><path class="st1" d="M46.4,6.6v5.7c0.5-0.4,1-0.7,1.6-0.9c0.6-0.2,1.2-0.3,1.8-0.3c1,0,1.8,0.3,2.4,0.9c0.6,0.6,0.9,1.4,0.9,2.3 V22h-3.2v-7.1c0-0.4-0.2-0.7-0.5-0.9c-0.3-0.3-0.7-0.4-1.1-0.4c-0.3,0-0.6,0.1-0.9,0.2c-0.4,0.2-0.7,0.4-1,0.6V22h-3.2V6.6H46.4z"></path><path class="st1" d="M37.9,22.2c-0.8,0-1.6,0-2.5-0.2c-0.8-0.2-1.5-0.4-2.2-0.8v-2.9c0.5,0.4,1.2,0.7,2,1c0.8,0.3,1.5,0.4,2,0.3 c0.4,0,0.7-0.1,0.9-0.3c0.2-0.2,0.3-0.3,0.3-0.5c0.1-0.4,0-0.7-0.3-0.9c-0.3-0.2-0.8-0.4-1.5-0.6c-0.8-0.2-1.5-0.5-1.9-0.8 c-0.5-0.3-0.8-0.6-1.1-1c-0.2-0.4-0.4-0.9-0.4-1.5c0-0.6,0.2-1.2,0.5-1.6c0.3-0.5,0.8-0.9,1.5-1.2c0.7-0.3,1.4-0.4,2.2-0.4 c0.6,0,1.2,0.1,1.8,0.2c0.6,0.1,1.1,0.3,1.5,0.4v2.6c-0.4-0.2-0.9-0.4-1.5-0.6c-0.6-0.2-1.1-0.3-1.5-0.3c-0.9,0-1.4,0.2-1.5,0.7 c0,0.3,0.1,0.5,0.4,0.7c0.3,0.2,0.7,0.4,1.3,0.6c0.8,0.3,1.5,0.5,2,0.8c0.5,0.3,0.9,0.6,1.2,1c0.3,0.4,0.4,1,0.4,1.6 c0,1-0.4,1.8-1.1,2.4C40,21.9,39,22.2,37.9,22.2z"></path><path class="st1" d="M25.8,22.3c-1,0-1.9-0.2-2.7-0.7c-0.7-0.5-1.3-1.1-1.7-2c-0.4-0.8-0.6-1.8-0.6-2.9c0-1.1,0.2-2.1,0.6-2.9 c0.4-0.9,1-1.5,1.7-2c0.7-0.5,1.6-0.7,2.6-0.7c0.5,0,0.9,0.1,1.4,0.3c0.5,0.2,0.9,0.4,1.3,0.7v-0.7h3.2v8.3c0,1.1,0.1,1.9,0.4,2.5 h-3c-0.1-0.2-0.2-0.4-0.2-0.7C27.9,21.9,26.9,22.3,25.8,22.3z M23.9,16.6c0,0.6,0.1,1.2,0.4,1.7c0.3,0.5,0.6,0.9,1,1.2 c0.4,0.3,0.8,0.4,1.3,0.4c0.3,0,0.7-0.1,1.1-0.2c0.4-0.1,0.7-0.5,1-0.9v-4.5c-0.3-0.5-0.6-0.8-1-0.9c-0.4-0.1-0.7-0.2-1.1-0.2 c-0.5,0-0.9,0.1-1.3,0.4c-0.4,0.3-0.7,0.7-1,1.2C24,15.4,23.9,16,23.9,16.6z"></path><path class="st1" d="M18.5,22.2c-1.2,0-2.1-0.3-2.7-1c-0.6-0.7-0.9-1.7-0.9-3V6.6H18v10.8c0,0.5,0,0.9,0.1,1.2 c0.1,0.3,0.2,0.5,0.4,0.6c0.1,0.1,0.3,0.2,0.5,0.2c0.2,0,0.5,0,1,0v2.6H18.5z"></path><path class="st1" d="M8.8,22.3c-1.5,0-2.9-0.3-4.1-0.8C3.6,20.9,2.7,20,2,19c-0.7-1.1-1-2.3-1-3.8c0-1.5,0.3-2.9,1-4 c0.7-1.1,1.6-2,2.7-2.6c1.2-0.6,2.5-0.9,4-0.9c0.7,0,1.5,0.1,2.3,0.2s1.4,0.3,1.9,0.6V11c-1.3-0.5-2.6-0.7-3.8-0.7 c-1.4,0-2.5,0.4-3.4,1.2c-0.9,0.8-1.3,2-1.3,3.7c0,0.9,0.2,1.7,0.6,2.3c0.4,0.7,1,1.2,1.7,1.6c0.7,0.4,1.4,0.6,2.2,0.6l0.4,0 c0.6,0,1.2-0.1,1.8-0.3c0.6-0.2,1.1-0.4,1.6-0.7v2.8c-0.6,0.3-1.2,0.5-1.8,0.6C10.4,22.2,9.6,22.3,8.8,22.3z"></path></g></g></svg></div></div><ul class="MuiList-root MuiList-padding the-menu css-1wduhak"><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters Mui-selected MuiListItemButton-root MuiListItemButton-gutters Mui-selected css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">首 页</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M2.06 10.06c.51.51 1.32.56 1.87.1 4.67-3.84 11.45-3.84 16.13-.01.56.46 1.38.42 1.89-.09.59-.59.55-1.57-.1-2.1-5.71-4.67-13.97-4.67-19.69 0-.65.52-.7 1.5-.1 2.1m7.76 7.76 1.47 1.47c.39.39 1.02.39 1.41 0l1.47-1.47c.47-.47.37-1.28-.23-1.59-1.22-.63-2.68-.63-3.91 0-.57.31-.68 1.12-.21 1.59m-3.73-3.73c.49.49 1.26.54 1.83.13 2.44-1.73 5.72-1.73 8.16 0 .57.4 1.34.36 1.83-.13l.01-.01c.6-.6.56-1.62-.13-2.11-3.44-2.49-8.13-2.49-11.58 0-.69.5-.73 1.51-.12 2.12"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">代 理</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 13H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4c0-1.1-.9-2-2-2M7 19c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2M19 3H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M7 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">订 阅</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2m6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56M12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96M4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2s.06 1.34.14 2zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56m2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8M12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96M14.34 14H9.66c-.09-.66-.16-1.32-.16-2s.07-1.35.16-2h4.68c.09.65.16 1.32.16 2s-.07 1.34-.16 2m.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56M16.36 14c.08-.66.14-1.32.14-2s-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2z"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">连 接</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M9 20c0 .55.45 1 1 1s1-.45 1-1v-3c.73-2.58 3.07-3.47 5.17-3l-.88.88c-.39.39-.39 1.02 0 1.41s1.02.39 1.41 0l2.59-2.59c.39-.39.39-1.02 0-1.41L16.7 9.7a.996.996 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41l.88.89c-1.51-.33-3.73.08-5.17 1.36V6.83l.88.88c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41L10.7 3.71a.996.996 0 0 0-1.41 0L6.71 6.29c-.39.39-.39 1.02 0 1.41s1.02.39 1.41 0L9 6.83z"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">规 则</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M13 17H5c-.55 0-1 .45-1 1s.45 1 1 1h8c.55 0 1-.45 1-1s-.45-1-1-1m6-8H5c-.55 0-1 .45-1 1s.45 1 1 1h14c.55 0 1-.45 1-1s-.45-1-1-1M5 15h14c.55 0 1-.45 1-1s-.45-1-1-1H5c-.55 0-1 .45-1 1s.45 1 1 1M4 6c0 .55.45 1 1 1h14c.55 0 1-.45 1-1s-.45-1-1-1H5c-.55 0-1 .45-1 1"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">日 志</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m6-5h-1V6c0-2.76-2.24-5-5-5-2.28 0-4.27 1.54-4.84 3.75-.14.54.18 1.08.72 1.22.53.14 1.08-.18 1.22-.72C9.44 3.93 10.63 3 12 3c1.65 0 3 1.35 3 3v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2m0 11c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-8c0-.55.45-1 1-1h10c.55 0 1 .45 1 1z"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">测 试</span></div></div></li><li class="MuiListItem-root MuiListItem-gutters MuiListItem-padding css-dh9epo"><div class="MuiButtonBase-root MuiListItemButton-root MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-gutters css-a7awl9" tabindex="0" role="button"><div class="MuiListItemIcon-root css-19m2841"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.5 12c0-.23-.01-.45-.03-.68l1.86-1.41c.4-.3.51-.86.26-1.3l-1.87-3.23c-.25-.44-.79-.62-1.25-.42l-2.15.91c-.37-.26-.76-.49-1.17-.68l-.29-2.31c-.06-.5-.49-.88-.99-.88h-3.73c-.51 0-.94.38-1 .88l-.29 2.31c-.41.19-.8.42-1.17.68l-2.15-.91c-.46-.2-1-.02-1.25.42L2.41 8.62c-.25.44-.14.99.26 1.3l1.86 1.41c-.02.22-.03.44-.03.67s.01.45.03.68l-1.86 1.41c-.4.3-.51.86-.26 1.3l1.87 3.23c.25.44.79.62 1.25.42l2.15-.91c.37.26.76.49 1.17.68l.29 2.31c.06.5.49.88.99.88h3.73c.5 0 .93-.38.99-.88l.29-2.31c.41-.19.8-.42 1.17-.68l2.15.91c.46.2 1 .02 1.25-.42l1.87-3.23c.25-.44.14-.99-.26-1.3l-1.86-1.41c.03-.23.04-.45.04-.68m-7.46 3.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5"></path></svg></div><div class="MuiListItemText-root css-2mzt1f"><span class="MuiTypography-root MuiTypography-body1 MuiListItemText-primary css-l7rop3">设 置</span></div></div></li></ul><div class="the-traffic"><div class="MuiBox-root css-79elbk"><div style="width: 100%; height: 60px; margin-bottom: 6px;"><canvas style="width: 100%; height: 100%;"></canvas></div><div class="MuiBox-root css-mbssy4"><div class="MuiBox-root css-2tjwzs" title="上传速度 "><svg class="MuiSvgIcon-root MuiSvgIcon-colorSecondary MuiSvgIcon-fontSizeMedium css-7pqngs" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M13 19V7.83l4.88 4.88c.39.39 1.03.39 1.42 0s.39-1.02 0-1.41l-6.59-6.59a.996.996 0 0 0-1.41 0l-6.6 6.58c-.39.39-.39 1.02 0 1.41s1.02.39 1.41 0L11 7.83V19c0 .55.45 1 1 1s1-.45 1-1"></path></svg><span class="MuiTypography-root MuiTypography-body1 css-qqjvq1">7.23</span><span class="MuiTypography-root MuiTypography-body1 css-z21y5g">KB/s</span></div><div class="MuiBox-root css-2tjwzs" title="下载速度 "><svg class="MuiSvgIcon-root MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeMedium css-6lz8vg" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11 5v11.17l-4.88-4.88c-.39-.39-1.03-.39-1.42 0s-.39 1.02 0 1.41l6.59 6.59c.39.39 1.02.39 1.41 0l6.59-6.59c.39-.39.39-1.02 0-1.41a.996.996 0 0 0-1.41 0L13 16.17V5c0-.55-.45-1-1-1s-1 .45-1 1"></path></svg><span class="MuiTypography-root MuiTypography-body1 css-gfs8d7">50.7</span><span class="MuiTypography-root MuiTypography-body1 css-z21y5g">KB/s</span></div><div class="MuiBox-root css-hfkxaa" title="内核占用  "><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-saklhp" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M14 9h-4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1m-1 4h-2v-2h2zm8-3c0-.55-.45-1-1-1h-1V7c0-1.1-.9-2-2-2h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v1h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v1H7c-1.1 0-2 .9-2 2v2H4c-.55 0-1 .45-1 1s.45 1 1 1h1v2H4c-.55 0-1 .45-1 1s.45 1 1 1h1v2c0 1.1.9 2 2 2h2v1c0 .55.45 1 1 1s1-.45 1-1v-1h2v1c0 .55.45 1 1 1s1-.45 1-1v-1h2c1.1 0 2-.9 2-2v-2h1c.55 0 1-.45 1-1s-.45-1-1-1h-1v-2h1c.55 0 1-.45 1-1m-5 7H8c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1h8c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1"></path></svg><span class="MuiTypography-root MuiTypography-body1 css-gp5n7o">73.0</span><span class="MuiTypography-root MuiTypography-body1 css-z21y5g">MB</span></div></div></div></div></div><div class="layout__right"><div class="the-bar"></div><div class="the-content"><div class="base-page"><header data-tauri-drag-region="true" style="user-select: none;"><p class="MuiTypography-root MuiTypography-body1 css-1l0zim6" data-tauri-drag-region="true">首 页</p><div class="MuiBox-root css-70qvj9"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorInherit MuiIconButton-sizeSmall css-1bsg61r" tabindex="0" type="button" aria-label="轻量模式"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M9 4v1.38c-.83-.33-1.72-.5-2.61-.5-1.79 0-3.58.68-4.95 2.05l3.33 3.33h1.11v1.11c.86.86 1.98 1.31 3.11 1.36V15H6v3c0 1.1.9 2 2 2h10c1.66 0 3-1.34 3-3V4zm-1.11 6.41V8.26H5.61L4.57 7.22a5.07 5.07 0 0 1 1.82-.34c1.34 0 2.59.52 3.54 1.46l1.41 1.41-.2.2c-.51.51-1.19.8-1.92.8-.47 0-.93-.12-1.33-.34M19 17c0 .55-.45 1-1 1s-1-.45-1-1v-2h-6v-2.59c.57-.23 1.1-.57 1.56-1.03l.2-.2L15.59 14H17v-1.41l-6-5.97V6h8z"></path></svg></button><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorInherit MuiIconButton-sizeSmall css-1bsg61r" tabindex="0" type="button" aria-label="使用手册"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m-1-4h2v2h-2zm1.61-9.96c-2.06-.3-3.88.97-4.43 2.79-.18.58.26 1.17.87 1.17h.2c.41 0 .74-.29.88-.67.32-.89 1.27-1.5 2.3-1.28.95.2 1.65 1.13 1.57 2.1-.1 1.34-1.62 1.63-2.45 2.88 0 .01-.01.01-.01.02-.01.02-.02.03-.03.05-.09.15-.18.32-.25.5-.01.03-.03.05-.04.08-.01.02-.01.04-.02.07-.12.34-.2.75-.2 1.25h2c0-.42.11-.77.28-1.07.02-.03.03-.06.05-.09.08-.14.18-.27.28-.39.01-.01.02-.03.03-.04.1-.12.21-.23.33-.34.96-.91 2.26-1.65 1.99-3.56-.24-1.74-1.61-3.21-3.35-3.47"></path></svg></button><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorInherit MuiIconButton-sizeSmall css-1bsg61r" tabindex="0" type="button" aria-label="首页设置"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.09-.16-.26-.25-.44-.25-.06 0-.12.01-.17.03l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1q-.09-.03-.18-.03c-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.09.16.26.25.44.25.06 0 .12-.01.17-.03l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1q.09.03.18.03c.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64zm-1.98-1.71c.04.31.05.52.05.73s-.02.43-.05.73l-.14 1.13.89.7 1.08.84-.7 1.21-1.27-.51-1.04-.42-.9.68c-.43.32-.84.56-1.25.73l-1.06.43-.16 1.13-.2 1.35h-1.4l-.19-1.35-.16-1.13-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7-1.06.43-1.27.51-.7-1.21 1.08-.84.89-.7-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13-.89-.7-1.08-.84.7-1.21 1.27.51 1.04.42.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43.16-1.13.2-1.35h1.39l.19 1.35.16 1.13 1.06.43c.43.18.83.41 1.23.71l.91.7 1.06-.43 1.27-.51.7 1.21-1.07.85-.89.7zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path></svg></button></div></header><div class="base-container" style="background-color: rgb(255, 255, 255);"><section style="background-color: var(--background-color);"><div class="base-content" style="padding: 2px;"><div class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-row MuiGrid-spacing-xs-1.5 css-6khhy5" data-rbd-droppable-id="home-cards" data-rbd-droppable-context-id="0"><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="profile" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="profile" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95C8.08 7.14 9.94 6 12 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11c1.56.1 2.78 1.41 2.78 2.96 0 1.65-1.35 3-3 3M8 13h2.55v3h2.9v-3H16l-4-4z"></path></svg></div><div class="MuiBox-root css-1xhmi63"><div class="MuiBox-root css-1t1qxzx"><button class="MuiTypography-root MuiTypography-h6 MuiLink-root MuiLink-underlineAlways MuiLink-button css-1m173tf" title="✨光速机场"><span>✨光速机场</span><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1onwa8j" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"></path></svg></button></div></div></div><div class="MuiBox-root css-1de8z80"><button class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary css-1v1lgbs" tabindex="0" type="button">订 阅<span class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeSmall css-qpmepm"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M2 20h20v-4H2zm2-3h2v2H4zM2 4v4h20V4zm4 3H4V5h2zm-4 7h20v-4H2zm2-3h2v2H4z"></path></svg></span></button></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-0"><div class="MuiStack-root css-1ov46kg"><div class="MuiStack-root css-csffzd"><svg class="MuiSvgIcon-root MuiSvgIcon-colorAction MuiSvgIcon-fontSizeSmall css-zp184r" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 15v4H5v-4zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1M7 18.5c-.82 0-1.5-.67-1.5-1.5s.68-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5M19 5v4H5V5zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1M7 8.5c-.82 0-1.5-.67-1.5-1.5S6.18 5.5 7 5.5s1.5.68 1.5 1.5S7.83 8.5 7 8.5"></path></svg><p class="MuiTypography-root MuiTypography-body2 MuiTypography-noWrap css-rwkm15"><span style="flex-shrink: 0;">来自: </span><button class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineAlways MuiLink-button css-1a95r94" title="gsurl.izenny.com"><span class="MuiTypography-root MuiTypography-body1 css-1woq51c">gsurl.izenny.com</span><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1onwa8j" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"></path></svg></button></p></div><div class="MuiStack-root css-csffzd"><svg class="MuiSvgIcon-root MuiSvgIcon-colorAction MuiSvgIcon-fontSizeSmall css-1lwu4fs" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11 8v5l4.25 2.52.77-1.28-3.52-2.09V8zm10 2V3l-2.64 2.64C16.74 4.01 14.49 3 12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9h-2c0 3.86-3.14 7-7 7s-7-3.14-7-7 3.14-7 7-7c1.93 0 3.68.79 4.95 2.05L14 10z"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-6tk3zt">更新时间: <span class="MuiBox-root css-10rvbm3">2025-08-09 09:18</span></p></div><div class="MuiStack-root css-csffzd"><svg class="MuiSvgIcon-root MuiSvgIcon-colorAction MuiSvgIcon-fontSizeSmall css-zp184r" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="m20.38 8.57-1.23 1.85a8 8 0 0 1-.22 7.58H5.07A8 8 0 0 1 15.58 6.85l1.85-1.23A10 10 0 0 0 3.35 19a2 2 0 0 0 1.72 1h13.85a2 2 0 0 0 1.74-1 10 10 0 0 0-.27-10.44z"></path><path d="M10.59 15.41a2 2 0 0 0 2.83 0l5.66-8.49-8.49 5.66a2 2 0 0 0 0 2.83"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">已使用 / 总量: <span class="MuiBox-root css-10rvbm3">7.16GB / 120GB</span></p></div><div class="MuiStack-root css-csffzd"><svg class="MuiSvgIcon-root MuiSvgIcon-colorAction MuiSvgIcon-fontSizeSmall css-zp184r" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 16H5V10h14zm0-12H5V6h14zm-7 5h5v5h-5z"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">到期时间: <span class="MuiBox-root css-10rvbm3">2026-08-06</span></p></div><div class="MuiBox-root css-164r41r"><span class="MuiTypography-root MuiTypography-caption css-5tie7k">6%</span><span class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-determinate css-1qazpsr" role="progressbar" aria-valuenow="6" aria-valuemin="0" aria-valuemax="100"><span class="MuiLinearProgress-bar MuiLinearProgress-bar1 MuiLinearProgress-barColorPrimary MuiLinearProgress-bar1Determinate css-1c5lrar" style="transform: translateX(-94%);"></span></span></div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="proxy" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="proxy" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><div class="MuiBox-root css-1cx3h7y" aria-label="未测试: -"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12 6c3.33 0 6.49 1.08 9.08 3.07L12 18.17l-9.08-9.1C5.51 7.08 8.67 6 12 6m0-2C7.31 4 3.07 5.9 0 8.98L12 21 24 8.98C20.93 5.9 16.69 4 12 4"></path></svg></div></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="当前节点">当前节点</h6></div></div><div class="MuiBox-root css-1de8z80"><div class="MuiBox-root css-70qvj9"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorInherit MuiIconButton-sizeSmall css-1cwtog7" tabindex="0" type="button" aria-label="按名称排序"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12.93 2.65c-.2-.2-.51-.2-.71 0l-2.01 2.01h4.72zm-.7 18.7c.2.2.51.2.71 0l1.98-1.98h-4.66zm-1.25-3.62c.6 0 1.01-.6.79-1.16L8.04 7.03c-.18-.46-.63-.76-1.12-.76s-.94.3-1.12.76l-3.74 9.53c-.22.56.19 1.16.79 1.16.35 0 .67-.22.8-.55l.71-1.9h5.11l.71 1.9c.13.34.45.56.8.56m-6.01-4.09 1.94-5.18 1.94 5.18zm16.08 2.5h-5.33l5.72-8.29c.46-.66-.02-1.57-.82-1.57h-6.48c-.44 0-.79.36-.79.8v.01c0 .44.36.8.79.8h5.09l-5.73 8.28c-.46.66.02 1.57.82 1.57h6.72c.44 0 .79-.36.79-.79.02-.45-.34-.81-.78-.81"></path></svg></button><button class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary css-1v1lgbs" tabindex="0" type="button">代 理<span class="MuiButton-icon MuiButton-endIcon MuiButton-iconSizeSmall css-qpmepm"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg></span></button></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-0"><div class="MuiBox-root css-6yc7vd"><div class="MuiBox-root css-0"><p class="MuiTypography-root MuiTypography-body1 css-u7chli">新加坡自动</p><div class="MuiBox-root css-1dtnjt5"><span class="MuiTypography-root MuiTypography-caption css-1rgabod">URLTest</span><div class="MuiChip-root MuiChip-filled MuiChip-sizeSmall MuiChip-colorPrimary MuiChip-filledPrimary css-jykzvo"><span class="MuiChip-label MuiChip-labelSmall css-b9zgoq">全局模式</span></div><div class="MuiChip-root MuiChip-outlined MuiChip-sizeSmall MuiChip-colorDefault MuiChip-outlinedDefault css-uf297f"><span class="MuiChip-label MuiChip-labelSmall css-oruufx">UDP</span></div></div></div><div class="MuiChip-root MuiChip-filled MuiChip-sizeSmall MuiChip-colorDefault MuiChip-filledDefault css-b63lk3"><span class="MuiChip-label MuiChip-labelSmall css-b9zgoq">-</span></div></div><div class="MuiFormControl-root MuiFormControl-fullWidth css-15bfsap"><label class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiFormLabel-filled MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined css-z7snei" data-shrink="true" id="proxy-group-select-label">代理组</label><div class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary Mui-disabled MuiInputBase-formControl MuiInputBase-sizeSmall MuiSelect-root css-1i3sesm"><div role="combobox" aria-disabled="true" aria-expanded="false" aria-haspopup="listbox" aria-labelledby="proxy-group-select-label" class="MuiSelect-select MuiSelect-outlined Mui-disabled MuiInputBase-input MuiOutlinedInput-input Mui-disabled MuiInputBase-inputSizeSmall css-j25tbx"><span class="notranslate" aria-hidden="true">&ZeroWidthSpace;</span></div><input aria-invalid="false" aria-hidden="true" tabindex="-1" disabled="" class="MuiSelect-nativeInput css-147e5lo" value="GLOBAL"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined Mui-disabled css-1l3b12y" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5z"></path></svg><fieldset aria-hidden="true" class="MuiOutlinedInput-notchedOutline css-5v2ak0"><legend class="css-w1u3ce"><span>代理组</span></legend></fieldset></div></div><div class="MuiFormControl-root MuiFormControl-fullWidth css-1giuhg3"><label class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiFormLabel-filled MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined css-z7snei" data-shrink="true" id="proxy-select-label">节点</label><div class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-sizeSmall MuiSelect-root css-1i3sesm"><div tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox" aria-labelledby="proxy-select-label" class="MuiSelect-select MuiSelect-outlined MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputSizeSmall css-j25tbx"><div class="MuiBox-root css-gg4vpm"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-n9jp4x">新加坡自动</p><div class="MuiChip-root MuiChip-filled MuiChip-sizeSmall MuiChip-colorDefault MuiChip-filledDefault css-b63lk3"><span class="MuiChip-label MuiChip-labelSmall css-b9zgoq">-</span></div></div></div><input aria-invalid="false" aria-hidden="true" tabindex="-1" class="MuiSelect-nativeInput css-147e5lo" value="新加坡自动"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconOutlined css-1l3b12y" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5z"></path></svg><fieldset aria-hidden="true" class="MuiOutlinedInput-notchedOutline css-5v2ak0"><legend class="css-w1u3ce"><span>节点</span></legend></fieldset></div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="network" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="network" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 15v4H5v-4zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1M7 18.5c-.82 0-1.5-.67-1.5-1.5s.68-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5M19 5v4H5V5zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1M7 8.5c-.82 0-1.5-.67-1.5-1.5S6.18 5.5 7 5.5s1.5.68 1.5 1.5S7.83 8.5 7 8.5"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="网络设置">网络设置</h6></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-1ofqig9"><div class="MuiStack-root css-1k84c6z"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation2 css-1hlek36" style="--Paper-shadow: none;"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H1c-.55 0-1 .45-1 1s.45 1 1 1h22c.55 0 1-.45 1-1s-.45-1-1-1zM5 6h14c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1H5c-.55 0-1-.45-1-1V7c0-.55.45-1 1-1"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-13tkkfv">系统代理</p></div><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-1woqkq7" style="--Paper-shadow: none;"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="m21.29 19.88-3.98-3.98c1.3-1.67 1.96-3.85 1.58-6.2-.54-3.41-3.33-6.14-6.75-6.62C7.57 2.44 3.61 5.69 3.07 10h2.02c.53-3.13 3.48-5.44 6.85-4.93 2.61.4 4.7 2.57 5.02 5.2C17.39 13.9 14.55 17 11 17c-2.42 0-4.5-1.44-5.45-3.5H3.4C4.45 16.69 7.46 19 11 19c1.85 0 3.55-.63 4.9-1.69l3.98 3.98c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41"></path><path d="m8.43 9.69 1.03 4.47c.*********** ********** 0 .87-.3 1.02-.74l1.01-3.04.69 1.66c.***********.92.62h.58c.41 0 .75-.34.75-.75s-.34-.75-.75-.75h-.23l-.97-2.34c-.17-.4-.56-.66-1-.66h-.05c-.46 0-.87.3-1.02.74l-.88 2.63-1.04-4.54C9.43 7.35 8.99 7 8.49 7c-.47 0-.89.31-1.03.76L6.45 11h-4.7c-.41 0-.75.34-.75.75s.34.75.75.75h5.07c.44 0 .82-.28.95-.7z"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-b01fhf">虚拟网卡模式</p><div class="MuiBox-root css-10goa4b"></div></div></div><div class="MuiBox-root css-f9z05k"><div class="MuiTypography-root MuiTypography-caption css-p84xk0" style="opacity: 1; transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1);">系统代理已关闭，建议大多数用户打开此选项<svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1p0tpdj" focusable="false" aria-hidden="true" viewBox="0 0 24 24" aria-label="修改操作系统的代理设置，如果开启失败，可手动修改操作系统的代理设置"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m-1-4h2v2h-2zm1.61-9.96c-2.06-.3-3.88.97-4.43 2.79-.18.58.26 1.17.87 1.17h.2c.41 0 .74-.29.88-.67.32-.89 1.27-1.5 2.3-1.28.95.2 1.65 1.13 1.57 2.1-.1 1.34-1.62 1.63-2.45 2.88 0 .01-.01.01-.01.02-.01.02-.02.03-.03.05-.09.15-.18.32-.25.5-.01.03-.03.05-.04.08-.01.02-.01.04-.02.07-.12.34-.2.75-.2 1.25h2c0-.42.11-.77.28-1.07.02-.03.03-.06.05-.09.08-.14.18-.27.28-.39.01-.01.02-.03.03-.04.1-.12.21-.23.33-.34.96-.91 2.26-1.65 1.99-3.56-.24-1.74-1.61-3.21-3.35-3.47"></path></svg></div></div><div class="MuiBox-root css-1tfu35h"><div class="MuiBox-root css-0"><div class="MuiBox-root css-6tmjot">系统代理</div><div class="MuiBox-root css-3vjhw9"><div class="MuiBox-root css-70qvj9"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1okqgrk" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M10 16c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1s-1 .45-1 1v6c0 .55.45 1 1 1m2-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m2-4c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1s-1 .45-1 1v6c0 .55.45 1 1 1"></path></svg><div class="MuiBox-root css-0"><h6 class="MuiTypography-root MuiTypography-subtitle1 css-1os43op">系统代理</h6></div></div><div class="MuiBox-root css-70qvj9"><div class="MuiBox-root css-w5pd9k" aria-label="修改操作系统的代理设置，如果开启失败，可手动修改操作系统的代理设置"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.5 12c0-.23-.01-.45-.03-.68l1.86-1.41c.4-.3.51-.86.26-1.3l-1.87-3.23c-.25-.44-.79-.62-1.25-.42l-2.15.91c-.37-.26-.76-.49-1.17-.68l-.29-2.31c-.06-.5-.49-.88-.99-.88h-3.73c-.51 0-.94.38-1 .88l-.29 2.31c-.41.19-.8.42-1.17.68l-2.15-.91c-.46-.2-1-.02-1.25.42L2.41 8.62c-.25.44-.14.99.26 1.3l1.86 1.41c-.02.22-.03.44-.03.67s.01.45.03.68l-1.86 1.41c-.4.3-.51.86-.26 1.3l1.87 3.23c.25.44.79.62 1.25.42l2.15-.91c.37.26.76.49 1.17.68l.29 2.31c.06.5.49.88.99.88h3.73c.5 0 .93-.38.99-.88l.29-2.31c.41-.19.8-.42 1.17-.68l2.15.91c.46.2 1 .02 1.25-.42l1.87-3.23c.25-.44.14-.99-.26-1.3l-1.86-1.41c.03-.23.04-.45.04-.68m-7.46 3.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5"></path></svg></div><span class="MuiSwitch-root MuiSwitch-edgeEnd MuiSwitch-sizeMedium css-156arp5"><span class="MuiButtonBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary PrivateSwitchBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary css-18944vg"><input class="PrivateSwitchBase-input MuiSwitch-input css-j8yymo" type="checkbox"><span class="MuiSwitch-thumb css-14rm2xa"></span></span><span class="MuiSwitch-track css-1khxa25"></span></span></div></div></div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="mode" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="mode" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M16 4.2c1.5 0 3 .6 4.2 1.7l.8-.8C19.6 3.7 17.8 3 16 3s-3.6.7-5 2.1l.8.8C13 4.8 14.5 4.2 16 4.2m-3.3 2.5.8.8c.7-.7 1.6-1 2.5-1s1.8.3 2.5 1l.8-.8c-.9-.9-2.1-1.4-3.3-1.4s-2.4.5-3.3 1.4M19 13h-2V9h-2v4H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4c0-1.1-.9-2-2-2m0 6H5v-4h14zM6 16h2v2H6zm3.5 0h2v2h-2zm3.5 0h2v2h-2z"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="代理模式">代理模式</h6></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-1ofqig9"><div class="MuiStack-root css-1g7xoiq"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-buo882" style="--Paper-shadow: none;"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M17 5.21c0-.45.54-.67.85-.35l2.79 2.79c.******** 0 .71l-2.79 2.79c-.31.31-.85.09-.85-.36V9h-3c-.55 0-1-.45-1-1s.45-1 1-1h3zM10 7c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1M6 7c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1m1 10h3c.55 0 1-.45 1-1s-.45-1-1-1H7v-1.79c0-.45-.54-.67-.85-.35l-2.79 2.79c-.2.2-.2.51 0 .71l2.79 2.79c.***********.85-.36zm7 0c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1m4 0c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-1jf2phu">规则</p></div><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation2 css-1cht9dg" style="--Paper-shadow: none;"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2m6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56M12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96M4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2s.06 1.34.14 2zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56m2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8M12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96M14.34 14H9.66c-.09-.66-.16-1.32-.16-2s.07-1.35.16-2h4.68c.09.65.16 1.32.16 2s-.07 1.34-.16 2m.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56M16.36 14c.08-.66.14-1.32.14-2s-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2z"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-17zua1j">全局</p></div><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-buo882" style="--Paper-shadow: none;"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="m21.71 11.29-9-9a.996.996 0 0 0-1.41 0l-9 9a.996.996 0 0 0 0 1.41l9 9c.39.39 1.02.39 1.41 0l9-9a.996.996 0 0 0 0-1.41M14 14.5V12h-4v2c0 .55-.45 1-1 1s-1-.45-1-1v-3c0-.55.45-1 1-1h5V7.5l3.15 3.15c.******** 0 .71z"></path></svg><p class="MuiTypography-root MuiTypography-body2 css-1jf2phu">直连</p></div></div><div class="MuiBox-root css-f9z05k"><div class="MuiTypography-root MuiTypography-caption css-xdthc8">所有流量均通过代理服务器，适用于需要全局科学上网的场景</div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-12 css-9mzodo" data-rbd-draggable-context-id="0" data-rbd-draggable-id="traffic" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="traffic" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-1f9dk5k"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="m20.38 8.57-1.23 1.85a8 8 0 0 1-.22 7.58H5.07A8 8 0 0 1 15.58 6.85l1.85-1.23A10 10 0 0 0 3.35 19a2 2 0 0 0 1.72 1h13.85a2 2 0 0 0 1.74-1 10 10 0 0 0-.27-10.44z"></path><path d="M10.59 15.41a2 2 0 0 0 2.83 0l5.66-8.49-8.49 5.66a2 2 0 0 0 0 2.83"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="流量统计">流量统计</h6></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-row MuiGrid-spacing-xs-1 css-8modhj"><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-12 css-j5005a"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-1unmb9x" style="--Paper-shadow: none;"><div style="height: 100%; position: relative;"><div class="MuiBox-root css-15emwx2"><canvas width="3103" height="227" style="width: 1773.29px; height: 130px; display: block;"></canvas><div class="MuiBox-root css-fdte1f"><div class="MuiBox-root css-1rrf92n">10 Minutes</div><div class="MuiBox-root css-p2zfy8"><div class="MuiBox-root css-zelwve">上传</div><div class="MuiBox-root css-1untsd5">下载</div></div><div class="MuiBox-root css-1rkt7ks">Smooth</div><div class="MuiBox-root css-g2ko2a">Points: 104 | Fresh: ✓ | Compressed: 20</div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-eymfee" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-1dp9ldj"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M13 19V7.83l4.88 4.88c.39.39 1.03.39 1.42 0s.39-1.02 0-1.41l-6.59-6.59a.996.996 0 0 0-1.41 0l-6.6 6.58c-.39.39-.39 1.02 0 1.41s1.02.39 1.41 0L11 7.83V19c0 .55.45 1 1 1s1-.45 1-1"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">上传速度</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">7.23</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj">KB/s</span></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-1n7w8rl" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-1j71sin"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11 5v11.17l-4.88-4.88c-.39-.39-1.03-.39-1.42 0s-.39 1.02 0 1.41l6.59 6.59c.39.39 1.02.39 1.41 0l6.59-6.59c.39-.39.39-1.02 0-1.41a.996.996 0 0 0-1.41 0L13 16.17V5c0-.55-.45-1-1-1s-1 .45-1 1"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">下载速度</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">50.7</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj">KB/s</span></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-8tzhsh" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-1dajjax"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M17 7h-3c-.55 0-1 .45-1 1s.45 1 1 1h3c1.65 0 3 1.35 3 3s-1.35 3-3 3h-3c-.55 0-1 .45-1 1s.45 1 1 1h3c2.76 0 5-2.24 5-5s-2.24-5-5-5m-9 5c0 .55.45 1 1 1h6c.55 0 1-.45 1-1s-.45-1-1-1H9c-.55 0-1 .45-1 1m2 3H7c-1.65 0-3-1.35-3-3s1.35-3 3-3h3c.55 0 1-.45 1-1s-.45-1-1-1H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h3c.55 0 1-.45 1-1s-.45-1-1-1"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">活跃连接</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">10</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj"></span></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-eymfee" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-1dp9ldj"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l4.65-4.65c.2-.2.51-.2.71 0L17 13z"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">上传量</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">1.36</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj">MB</span></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-1n7w8rl" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-1j71sin"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M17 13l-4.65 4.65c-.2.2-.51.2-.71 0L7 13h3V9h4v4z"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">下载量</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">246</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj">MB</span></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-4 css-fiq9q0"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 css-kmp0fy" style="--Paper-shadow: none;"><div class="MuiGrid-root MuiGrid-direction-xs-row css-jhdkkv"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M14 9h-4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1m-1 4h-2v-2h2zm8-3c0-.55-.45-1-1-1h-1V7c0-1.1-.9-2-2-2h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v1h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v1H7c-1.1 0-2 .9-2 2v2H4c-.55 0-1 .45-1 1s.45 1 1 1h1v2H4c-.55 0-1 .45-1 1s.45 1 1 1h1v2c0 1.1.9 2 2 2h2v1c0 .55.45 1 1 1s1-.45 1-1v-1h2v1c0 .55.45 1 1 1s1-.45 1-1v-1h2c1.1 0 2-.9 2-2v-2h1c.55 0 1-.45 1-1s-.45-1-1-1h-1v-2h1c.55 0 1-.45 1-1m-5 7H8c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1h8c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1"></path></svg></div><div class="MuiGrid-root MuiGrid-direction-xs-row css-a49te1"><span class="MuiTypography-root MuiTypography-caption MuiTypography-noWrap css-1mnjlr1">内核占用</span><div class="MuiGrid-root MuiGrid-direction-xs-row css-1wkbnid"><p class="MuiTypography-root MuiTypography-body1 MuiTypography-noWrap css-rax6ql">73.0</p><span class="MuiTypography-root MuiTypography-caption css-ygipvj">MB</span></div></div></div></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="test" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="test" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 5.5c0-.28-.22-.5-.5-.5M1 9l2 2c2.88-2.88 6.79-4.08 10.53-3.62l1.19-2.68C9.89 3.84 4.74 5.27 1 9m20 2 2-2c-1.64-1.64-3.55-2.82-5.59-3.57l-.53 2.82c1.5.62 2.9 1.53 4.12 2.75m-4 4 2-2c-.8-.8-1.7-1.42-2.66-1.89l-.55 2.92c.42.27.83.59 1.21.97M5 13l2 2c1.13-1.13 2.56-1.79 4.03-2l1.28-2.88c-2.63-.08-5.3.87-7.31 2.88"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="网站测试">网站测试</h6></div></div><div class="MuiBox-root css-1de8z80"><div class="MuiBox-root css-1i27l4i"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-xz9haa" tabindex="0" type="button" aria-label="测试全部"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 5.5c0-.28-.22-.5-.5-.5M1 9l2 2c2.88-2.88 6.79-4.08 10.53-3.62l1.19-2.68C9.89 3.84 4.74 5.27 1 9m20 2 2-2c-1.64-1.64-3.55-2.82-5.59-3.57l-.53 2.82c1.5.62 2.9 1.53 4.12 2.75m-4 4 2-2c-.8-.8-1.7-1.42-2.66-1.89l-.55 2.92c.42.27.83.59 1.21.97M5 13l2 2c1.13-1.13 2.56-1.79 4.03-2l1.28-2.88c-2.63-.08-5.3.87-7.31 2.88"></path></svg></button><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-xz9haa" tabindex="0" type="button" aria-label="新建测试"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"></path></svg></button></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-19bkyh4"><div class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-row MuiGrid-spacing-xs-1 css-19hkpd7"><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-3 css-1dlpkjl"><div class="MuiBox-root css-83xgas"><div class="MuiBox-root css-ehj8cx"><div class="MuiBox-root css-15d3q2k" role="button" tabindex="0" aria-disabled="false" aria-roledescription="sortable" aria-describedby="DndDescribedBy-0"><div class="MuiBox-root css-1l4w6pd"><img height="40px" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48cGF0aCBkPSJNMTYuMTI1IDFjLTEuMTUzLjA2Ny0yLjQ3Ny43MS0zLjI2NCAxLjUyNy0uNzEuNzQ0LTEuMjcyIDEuODUtMS4wNDMgMi45MTggMS4yNTMuMDMzIDIuNTExLS42MjYgMy4yNjQtMS40NTkuNzAzLS43NzkgMS4yMzYtMS44NjYgMS4wNDMtMi45ODZ6bS4wNjggNC40NDNjLTEuODA5IDAtMi41NjUgMS4xMTItMy44MTggMS4xMTItMS4yODkgMC0yLjQ2Ny0xLjA0MS00LjAyNy0xLjA0MUM2LjIyNiA1LjUxNCAzIDcuNDggMyAxMi4xMSAzIDE2LjMyNCA2LjgxOCAyMSA4Ljk3MyAyMWMxLjMwOS4wMTMgMS42MjYtLjgyMyAzLjQwMi0uODMyIDEuNzc4LS4wMTMgMi4xNjIuODQzIDMuNDczLjgzMiAxLjQ3Ni0uMDExIDIuNjI4LTEuNjMzIDMuNDctMi45MTguNjA0LS45Mi44NTMtMS4zOSAxLjMyLTIuNDMtMy40NzItLjg4LTQuMTYzLTYuNDggMC03LjYzOC0uNzg1LTEuMzQxLTMuMDgtMi41Ny00LjQ0NS0yLjU3eiIvPjwvc3ZnPg=="></div><div class="MuiBox-root css-1l4w6pd">Apple</div></div><hr class="MuiDivider-root MuiDivider-fullWidth css-hrwi5s"><div class="MuiBox-root css-1ow0d30"><div class="the-check MuiBox-root css-gb3sqg">测试</div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-3 css-1dlpkjl"><div class="MuiBox-root css-83xgas"><div class="MuiBox-root css-ehj8cx"><div class="MuiBox-root css-15d3q2k" role="button" tabindex="0" aria-disabled="false" aria-roledescription="sortable" aria-describedby="DndDescribedBy-0"><div class="MuiBox-root css-1l4w6pd"><img height="40px" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMCAzMCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48cGF0aCBkPSJNMTUgM0M4LjM3MyAzIDMgOC4zNzMgMyAxNWMwIDUuNjIzIDMuODcyIDEwLjMyOCA5LjA5MiAxMS42M2ExLjc1MSAxLjc1MSAwIDAgMS0uMDkyLS41ODN2LTIuMDUxaC0xLjUwOGMtLjgyMSAwLTEuNTUxLS4zNTMtMS45MDUtMS4wMDktLjM5My0uNzI5LS40NjEtMS44NDQtMS40MzUtMi41MjYtLjI4OS0uMjI3LS4wNjktLjQ4Ni4yNjQtLjQ1MS42MTUuMTc0IDEuMTI1LjU5NiAxLjYwNSAxLjIyMi40NzguNjI3LjcwMy43NjkgMS41OTYuNzY5LjQzMyAwIDEuMDgxLS4wMjUgMS42OTEtLjEyMS4zMjgtLjgzMy44OTUtMS42IDEuNTg4LTEuOTYyLTMuOTk2LS40MTEtNS45MDMtMi4zOTktNS45MDMtNS4wOTggMC0xLjE2Mi40OTUtMi4yODYgMS4zMzYtMy4yMzMtLjI3Ni0uOTQtLjYyMy0yLjg1Ny4xMDYtMy41ODcgMS43OTggMCAyLjg4NSAxLjE2NiAzLjE0NiAxLjQ4MUE4Ljk5MyA4Ljk5MyAwIDAgMSAxNS40OTUgOWMxLjAzNiAwIDIuMDI0LjE3NCAyLjkyMi40ODNDMTguNjc1IDkuMTcgMTkuNzYzIDggMjEuNTY1IDhjLjczMi43MzEuMzgxIDIuNjU2LjEwMiAzLjU5NC44MzYuOTQ1IDEuMzI4IDIuMDY2IDEuMzI4IDMuMjI2IDAgMi42OTctMS45MDQgNC42ODQtNS44OTQgNS4wOTdDMTguMTk5IDIwLjQ5IDE5IDIyLjEgMTkgMjMuMzEzdjIuNzM0YzAgLjEwNC0uMDIzLjE3OS0uMDM1LjI2OEMyMy42NDEgMjQuNjc2IDI3IDIwLjIzNiAyNyAxNWMwLTYuNjI3LTUuMzczLTEyLTEyLTEyeiIvPjwvc3ZnPg=="></div><div class="MuiBox-root css-1l4w6pd">GitHub</div></div><hr class="MuiDivider-root MuiDivider-fullWidth css-hrwi5s"><div class="MuiBox-root css-1ow0d30"><div class="the-check MuiBox-root css-gb3sqg">测试</div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-3 css-1dlpkjl"><div class="MuiBox-root css-83xgas"><div class="MuiBox-root css-ehj8cx"><div class="MuiBox-root css-15d3q2k" role="button" tabindex="0" aria-disabled="false" aria-roledescription="sortable" aria-describedby="DndDescribedBy-0"><div class="MuiBox-root css-1l4w6pd"><img height="40px" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48cGF0aCBmaWxsPSIjRkZDMTA3IiBkPSJNNDMuNjExIDIwLjA4M0g0MlYyMEgyNHY4aDExLjMwM2MtMS42NDkgNC42NTctNi4wOCA4LTExLjMwMyA4LTYuNjI3IDAtMTItNS4zNzMtMTItMTJzNS4zNzMtMTIgMTItMTJjMy4wNTkgMCA1Ljg0MiAxLjE1NCA3Ljk2MSAzLjAzOWw1LjY1Ny01LjY1N0MzNC4wNDYgNi4wNTMgMjkuMjY4IDQgMjQgNCAxMi45NTUgNCA0IDEyLjk1NSA0IDI0czguOTU1IDIwIDIwIDIwIDIwLTguOTU1IDIwLTIwYzAtMS4zNDEtLjEzOC0yLjY1LS4zODktMy45MTd6Ii8+PHBhdGggZmlsbD0iI0ZGM0QwMCIgZD0iTTYuMzA2IDE0LjY5MWw2LjU3MSA0LjgxOUMxNC42NTUgMTUuMTA4IDE4Ljk2MSAxMiAyNCAxMmMzLjA1OSAwIDUuODQyIDEuMTU0IDcuOTYxIDMuMDM5bDUuNjU3LTUuNjU3QzM0LjA0NiA2LjA1MyAyOS4yNjggNCAyNCA0IDE2LjMxOCA0IDkuNjU2IDguMzM3IDYuMzA2IDE0LjY5MXoiLz48cGF0aCBmaWxsPSIjNENBRjUwIiBkPSJNMjQgNDRjNS4xNjYgMCA5Ljg2LTEuOTc3IDEzLjQwOS01LjE5MmwtNi4xOS01LjIzOEExMS45MSAxMS45MSAwIDAgMSAyNCAzNmMtNS4yMDIgMC05LjYxOS0zLjMxNy0xMS4yODMtNy45NDZsLTYuNTIyIDUuMDI1QzkuNTA1IDM5LjU1NiAxNi4yMjcgNDQgMjQgNDR6Ii8+PHBhdGggZmlsbD0iIzE5NzZEMiIgZD0iTTQzLjYxMSAyMC4wODNINDJWMjBIMjR2OGgxMS4zMDNhMTIuMDQgMTIuMDQgMCAwIDEtNC4wODcgNS41NzFsLjAwMy0uMDAyIDYuMTkgNS4yMzhDMzYuOTcxIDM5LjIwNSA0NCAzNCA0NCAyNGMwLTEuMzQxLS4xMzgtMi42NS0uMzg5LTMuOTE3eiIvPjwvc3ZnPg=="></div><div class="MuiBox-root css-1l4w6pd">Google</div></div><hr class="MuiDivider-root MuiDivider-fullWidth css-hrwi5s"><div class="MuiBox-root css-1ow0d30"><div class="the-check MuiBox-root css-gb3sqg">测试</div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-3 css-1dlpkjl"><div class="MuiBox-root css-83xgas"><div class="MuiBox-root css-ehj8cx"><div class="MuiBox-root css-15d3q2k" role="button" tabindex="0" aria-disabled="false" aria-roledescription="sortable" aria-describedby="DndDescribedBy-0"><div class="MuiBox-root css-1l4w6pd"><img height="40px" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48cGF0aCBmaWxsPSIjRkYzRDAwIiBkPSJNNDMuMiAzMy45Yy0uNCAyLjEtMi4xIDMuNy00LjIgNC0zLjMuNS04LjggMS4xLTE1IDEuMS02LjEgMC0xMS42LS42LTE1LTEuMS0yLjEtLjMtMy44LTEuOS00LjItNC0uNC0yLjMtLjgtNS43LS44LTkuOXMuNC03LjYuOC05LjljLjQtMi4xIDIuMS0zLjcgNC4yLTRDMTIuMyA5LjYgMTcuOCA5IDI0IDljNi4yIDAgMTEuNi42IDE1IDEuMSAyLjEuMyAzLjggMS45IDQuMiA0IC40IDIuMy45IDUuNy45IDkuOS0uMSA0LjItLjUgNy42LS45IDkuOXoiLz48cGF0aCBmaWxsPSIjRkZGIiBkPSJNMjAgMzFWMTdsMTIgN3oiLz48L3N2Zz4="></div><div class="MuiBox-root css-1l4w6pd">Youtube</div></div><hr class="MuiDivider-root MuiDivider-fullWidth css-hrwi5s"><div class="MuiBox-root css-1ow0d30"><div class="the-check MuiBox-root css-gb3sqg">测试</div></div></div></div></div></div><div id="DndDescribedBy-0" style="display: none;">
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  </div><div id="DndLiveRegion-0" role="status" aria-live="assertive" aria-atomic="true" style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(100%); white-space: nowrap;"></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="ip" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="ip" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-9j8v3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7M7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 2.88-2.88 7.19-5 9.88C9.92 16.21 7 11.85 7 9"></path><circle cx="12" cy="9" r="2.5"></circle></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="IP 信息">IP 信息</h6></div></div><div class="MuiBox-root css-1de8z80"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-xz9haa" tabindex="0" type="button"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"></path></svg></button></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiBox-root css-1sm2s1z"><div class="MuiBox-root css-1rq6zql"><div class="MuiBox-root css-1gh9js6"><div class="MuiBox-root css-eyptl"><span class="MuiBox-root css-1919i81">🇸🇬</span><h6 class="MuiTypography-root MuiTypography-subtitle1 css-1pbdznn">Singapore</h6></div><div class="MuiBox-root css-kmnzss"><p class="MuiTypography-root MuiTypography-body2 css-zdrk7j">IP:</p><div class="MuiBox-root css-lxlk2v"><p class="MuiTypography-root MuiTypography-body2 css-1323qxh">••••••••••</p><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-xz9haa" tabindex="0" type="button"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M12 6c3.79 0 7.17 2.13 8.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5C4.83 8.13 8.21 6 12 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4m0 5c1.38 0 2.5 1.12 2.5 2.5S13.38 14 12 14s-2.5-1.12-2.5-2.5S10.62 9 12 9m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7"></path></svg></button></div></div><div class="MuiBox-root css-u47bco"><p class="MuiTypography-root MuiTypography-body2 css-1b736x9">自治域:</p><p class="MuiTypography-root MuiTypography-body2 css-181eqdf">AS9009</p></div></div><div class="MuiBox-root css-1lv3kkr"><div class="MuiBox-root css-u47bco"><p class="MuiTypography-root MuiTypography-body2 css-1b736x9">服务商:</p><p class="MuiTypography-root MuiTypography-body2 css-181eqdf">Unknown</p></div><div class="MuiBox-root css-u47bco"><p class="MuiTypography-root MuiTypography-body2 css-1b736x9">组织:</p><p class="MuiTypography-root MuiTypography-body2 css-181eqdf">M247 Europe SRL</p></div><div class="MuiBox-root css-u47bco"><p class="MuiTypography-root MuiTypography-body2 css-1b736x9">位置:</p><p class="MuiTypography-root MuiTypography-body2 css-181eqdf">Singapore, Singapore</p></div><div class="MuiBox-root css-u47bco"><p class="MuiTypography-root MuiTypography-body2 css-1b736x9">时区:</p><p class="MuiTypography-root MuiTypography-body2 css-181eqdf">Asia/Singapore</p></div></div></div><div class="MuiBox-root css-u7jnix"><span class="MuiTypography-root MuiTypography-caption css-1ukdhxe">自动刷新: 90s</span><span class="MuiTypography-root MuiTypography-caption css-105op4k">SG, 103.85, 1.29</span></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="clashinfo" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="clashinfo" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-1kl0a0c"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M22 9V7h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2v-2h-2V9zm-4 10H4V5h14zM6 13h5v4H6zm6-6h4v3h-4zM6 7h5v5H6zm6 4h4v6h-4z"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="Clash 信息">Clash 信息</h6></div></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiStack-root css-pb4y8c"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">内核版本</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">v1.19.12 Mihomo</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">系统代理地址</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">127.0.0.1:7897</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">混合代理端口</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">7897</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">运行时间</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">0:03:30</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">规则数量</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">16</p></div></div></div></div></div><div class="MuiGrid-root MuiGrid-direction-xs-row MuiGrid-grid-xs-6 css-nxl1g5" data-rbd-draggable-context-id="0" data-rbd-draggable-id="systeminfo" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="systeminfo" data-rbd-drag-handle-context-id="0" draggable="false"><div class="MuiBox-root css-23cvz"><div class="MuiBox-root css-rbmxes"><div class="MuiBox-root css-14zl6yh"><div class="MuiBox-root css-y9p1lb"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-q7mezt" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"></path></svg></div><div class="MuiBox-root css-1xhmi63"><h6 class="MuiTypography-root MuiTypography-h6 css-wlcuw7" title="系统信息">系统信息</h6></div></div><div class="MuiBox-root css-1de8z80"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-xz9haa" tabindex="0" type="button" title="设置"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-vh810p" focusable="false" aria-hidden="true" viewBox="0 0 24 24"><path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.09-.16-.26-.25-.44-.25-.06 0-.12.01-.17.03l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1q-.09-.03-.18-.03c-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.09.16.26.25.44.25.06 0 .12-.01.17-.03l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1q.09.03.18.03c.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64zm-1.98-1.71c.04.31.05.52.05.73s-.02.43-.05.73l-.14 1.13.89.7 1.08.84-.7 1.21-1.27-.51-1.04-.42-.9.68c-.43.32-.84.56-1.25.73l-1.06.43-.16 1.13-.2 1.35h-1.4l-.19-1.35-.16-1.13-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7-1.06.43-1.27.51-.7-1.21 1.08-.84.89-.7-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13-.89-.7-1.08-.84.7-1.21 1.27.51 1.04.42.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43.16-1.13.2-1.35h1.39l.19 1.35.16 1.13 1.06.43c.43.18.83.41 1.23.71l.91.7 1.06-.43 1.27-.51.7 1.21-1.07.85-.89.7zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path></svg></button></div></div><div class="MuiBox-root css-1frdjpe"><div class="MuiStack-root css-pb4y8c"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">操作系统信息</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">Windows 10 Pro</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-95g4uk"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">开机自启</p><div class="MuiStack-root css-csffzd"><div class="MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeSmall MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-1rhk3jo" tabindex="0" role="button"><span class="MuiChip-label MuiChip-labelSmall css-oruufx">未启用</span></div></div></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-95g4uk"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">运行模式</p><p class="MuiTypography-root MuiTypography-body2 css-18ma7i4"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-vggsnp" focusable="false" role="img" viewBox="0 0 24 24"><path d="M19 15v4H5v-4zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1M7 18.5c-.82 0-1.5-.67-1.5-1.5s.68-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5M19 5v4H5V5zm1-2H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1M7 8.5c-.82 0-1.5-.67-1.5-1.5S6.18 5.5 7 5.5s1.5.68 1.5 1.5S7.83 8.5 7 8.5"></path><title>服务模式</title></svg>服务模式</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">最后检查更新</p><p class="MuiTypography-root MuiTypography-body2 css-6se63c">2025/8/9 09:41:15</p></div><hr class="MuiDivider-root MuiDivider-fullWidth css-9k49o2"><div class="MuiStack-root css-9jay18"><p class="MuiTypography-root MuiTypography-body2 css-ea6efl">Verge 版本</p><p class="MuiTypography-root MuiTypography-body2 css-1q9mi9l">v2.4.0+autobuild.0801.3eb2a5b</p></div></div></div></div></div></div></div></section></div></div></div></div></div></div>

  

<div id="rbd-announcement-0" aria-live="assertive" aria-atomic="true" style="position: absolute; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(100%);"></div><div id="rbd-hidden-text-0-hidden-text-0" style="display: none;">
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
</div></body></html>