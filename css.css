/*



*/
section {
  background-color: rgba(255, 255, 255, 0) !important;
}


html {
font-size: 22px;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
--background-color: #f0f0f000 !important;
}

.base-container {
  background-color: #f0f0f000 !important;
}


.MuiTypography-root {
  background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
  background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*浅色背景底色*/
.css-1li7dvq::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.css-l3ykv8 {
  position: relative;
  background-color: rgba(255, 255, 255, 0) !important;
  color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: -2;
}

/*深色背景底色*/
.css-l3ykv8::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: -1;
}


/* 主题模式和变量定义 */
:root {
/* 浅色模式变量 */

--border-light: rgba(0, 0, 0, 0.08);
--shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
--shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
--shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);


/* 布局变量 */
--base-spacing: 1.5rem;
--grid-gap: 1.2rem;
--card-min-width: 280px;
--sidebar-width: 280px;
--header-height: 64px;
--border-radius: 12px;
--border-radius-small: 8px;
--border-radius-large: 16px;
--nav-item-width: 220px;
--nav-item-border-radius: 30px;
--card-bg-light: rgba(255, 255, 255, 0.85);
--card-bg-dark: rgba(33, 33, 33, 0.85);
--card-bg-light-hover: rgba(255, 255, 255, 0.05);
--card-bg-dark-hover: rgba(33, 33, 33, 0.05);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
:root:not([data-theme="light"]):not(.light-mode) {

  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-inverse: rgba(33, 37, 41, 0.95);

  --border-light: rgba(255, 255, 255, 0.08);

  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.5);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);


}
}


/* 左侧导航栏 */
.layout__left{
flex: 0 0 var(--sidebar-width);
background: var(--background-glass);
backdrop-filter: var(--blur-medium);
border-right: 1px solid var(--border-light);
padding: var(--base-spacing);
min-width: 360px;
max-width: 420px;
box-shadow: var(--shadow-light);
position: relative;
z-index: 10;
}


/* ========================================
 响应式网格系统
 ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
gap: var(--grid-gap);
padding: var(--grid-gap);
width: 100%;
align-items: start;
}

/* 网格项目 */
.grid-item {
display: flex;
flex-direction: column;
min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--base-spacing);
margin: var(--base-spacing) 0;
}


/* ========================================
 Material-UI 组件优化
 ======================================== */

/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
border: 1px solid var(--border-light) !important;
backdrop-filter: var(--blur-light);
border-radius: var(--border-radius-small) !important;

box-shadow: var(--shadow-light) !important;
}

/* ========================================
 赛博朋克卡片悬停特效
 ======================================== */

/* 定义赛博朋克风格的颜色和动画变量 */
:root {
--cyber-c1: #00fffc; /* 亮青色 */
--cyber-c2: #ff00ff; /* 品红色 */
--cyber-c3: #faff00; /* 亮黄色 */
--cyber-border-angle: 0deg;
/* 革命性粒子颜色系统 - 100种颜色，11个核心粒子 */
--cyber-p1: #ff0000; --cyber-p2: #ff1a00; --cyber-p3: #ff3300; --cyber-p4: #ff4d00; --cyber-p5: #ff6600;
--cyber-p6: #ff8000; --cyber-p7: #ff9900; --cyber-p8: #ffb300; --cyber-p9: #ffcc00; --cyber-p10: #ffe600;
--cyber-p11: #ffff00; --cyber-p12: #e6ff00; --cyber-p13: #ccff00; --cyber-p14: #b3ff00; --cyber-p15: #99ff00;
--cyber-p16: #80ff00; --cyber-p17: #66ff00; --cyber-p18: #4dff00; --cyber-p19: #33ff00; --cyber-p20: #1aff00;
--cyber-p21: #00ff00; --cyber-p22: #00ff1a; --cyber-p23: #00ff33; --cyber-p24: #00ff4d; --cyber-p25: #00ff66;
--cyber-p26: #00ff80; --cyber-p27: #00ff99; --cyber-p28: #00ffb3; --cyber-p29: #00ffcc; --cyber-p30: #00ffe6;
--cyber-p31: #00ffff; --cyber-p32: #00e6ff; --cyber-p33: #00ccff; --cyber-p34: #00b3ff; --cyber-p35: #0099ff;
--cyber-p36: #0080ff; --cyber-p37: #0066ff; --cyber-p38: #004dff; --cyber-p39: #0033ff; --cyber-p40: #001aff;
--cyber-p41: #0000ff; --cyber-p42: #1a00ff; --cyber-p43: #3300ff; --cyber-p44: #4d00ff; --cyber-p45: #6600ff;
--cyber-p46: #8000ff; --cyber-p47: #9900ff; --cyber-p48: #b300ff; --cyber-p49: #cc00ff; --cyber-p50: #e600ff;
--cyber-p51: #ff00ff; --cyber-p52: #ff00e6; --cyber-p53: #ff00cc; --cyber-p54: #ff00b3; --cyber-p55: #ff0099;
--cyber-p56: #ff0080; --cyber-p57: #ff0066; --cyber-p58: #ff004d; --cyber-p59: #ff0033; --cyber-p60: #ff001a;
--cyber-p61: #ff4080; --cyber-p62: #ff8040; --cyber-p63: #ffbf40; --cyber-p64: #dfff40; --cyber-p65: #9fff40;
--cyber-p66: #60ff40; --cyber-p67: #40ff60; --cyber-p68: #40ff9f; --cyber-p69: #40ffdf; --cyber-p70: #40bfff;
--cyber-p71: #4080ff; --cyber-p72: #4040ff; --cyber-p73: #8040ff; --cyber-p74: #bf40ff; --cyber-p75: #ff40df;
--cyber-p76: #ff409f; --cyber-p77: #ff4060; --cyber-p78: #ff6040; --cyber-p79: #ff9f40; --cyber-p80: #ffdf40;
--cyber-p81: #dfff80; --cyber-p82: #9fff80; --cyber-p83: #60ff80; --cyber-p84: #40ff80; --cyber-p85: #40ff9f;
--cyber-p86: #40ffbf; --cyber-p87: #40ffdf; --cyber-p88: #40dfff; --cyber-p89: #40bfff; --cyber-p90: #409fff;
--cyber-p91: #4080ff; --cyber-p92: #4060ff; --cyber-p93: #6040ff; --cyber-p94: #8040ff; --cyber-p95: #9f40ff;
--cyber-p96: #bf40ff; --cyber-p97: #df40ff; --cyber-p98: #ff40df; --cyber-p99: #ff40bf; --cyber-p100: #ff409f;
/* 核心11个粒子选择 - 动态颜色轮换系统 */
--particle-1: var(--cyber-p5); --particle-2: var(--cyber-p15); --particle-3: var(--cyber-p25); --particle-4: var(--cyber-p35);
--particle-5: var(--cyber-p45); --particle-6: var(--cyber-p55); --particle-7: var(--cyber-p65); --particle-8: var(--cyber-p75);
--particle-9: var(--cyber-p85); --particle-10: var(--cyber-p95); --particle-11: var(--cyber-p50);
/* 动态颜色组 - 用于轮换效果 */
--color-group-1: var(--cyber-p1), var(--cyber-p21), var(--cyber-p41), var(--cyber-p61), var(--cyber-p81);
--color-group-2: var(--cyber-p10), var(--cyber-p30), var(--cyber-p50), var(--cyber-p70), var(--cyber-p90);
--color-group-3: var(--cyber-p5), var(--cyber-p25), var(--cyber-p45), var(--cyber-p65), var(--cyber-p85);
}

/* 注册 CSS 变量以实现平滑动画 */
@property --cyber-border-angle {
syntax: '<angle>';
inherits: false;
initial-value: 0deg;
}

/* 统一所有卡片和按钮的基础样式，为特效做准备 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j,
.main .bg-primary-foreground, .side .bg-primary-foreground,
.main .bg-foreground, .side .bg-foreground,
.main .bg-content1, .side .bg-content1,
.main .bg-default, .side .bg-default,
.MuiButton-root, .MuiListItemButton-root {
transition: transform 0.3s ease, background-color 0.3s ease !important;
position: relative !important;
overflow: hidden !important; /* 包含内部特效 */
z-index: 1 !important;
}

/* 为特定卡片设置背景和圆角 */
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
background-color: var(--card-bg-light) !important;
border-radius: var(--border-radius) !important;
}

/* 深色模式下的卡片背景 */
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y, .css-1rgmi2n, .css-bjjbb7, .css-hds0vx,
.css-aafiep, .css-xd8r7u, .css-ya2z3b, .css-8sla8j, .css-ulr2qx, .css-17rlh6j {
  background-color: var(--card-bg-dark) !important;
}
}

/* 动态边框的容器 - 使用 ::before */
.MuiBox-root .css-1ow8u3y::before, .css-1rgmi2n::before, .css-bjjbb7::before, .css-hds0vx::before,
.css-aafiep::before, .css-xd8r7u::before, .css-ya2z3b::before, .css-8sla8j::before, .css-ulr2qx::before, .css-17rlh6j::before,
.main .bg-primary-foreground::before, .side .bg-primary-foreground::before,
.main .bg-foreground::before, .side .bg-foreground::before,
.main .bg-content1::before, .side .bg-content1::before,
.main .bg-default::before, .side .bg-default::before,
.MuiButton-root::before, .MuiListItemButton-root::before {
content: '' !important;
position: absolute !important;
top: 0; left: 0; right: 0; bottom: 0;
border-radius: inherit !important; /* 继承父元素的圆角 */
padding: 2px !important; /* 边框宽度 */
background: conic-gradient(from var(--cyber-border-angle), var(--cyber-c2), var(--cyber-c1), var(--cyber-c3), var(--cyber-c2)) !important;
-webkit-mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
mask:
  linear-gradient(#fff 0 0) content-box,
  linear-gradient(#fff 0 0) !important;
-webkit-mask-composite: xor !important;
mask-composite: exclude !important;
animation: cyberpunk-border-flow 4s linear infinite !important;
opacity: 0 !important;
transition: opacity 0.4s ease-in-out !important;
z-index: -1 !important;
pointer-events: none !important;
}

/* 全新弧形轨迹粒子效果 - 360度绽放覆盖整个卡片 */
.MuiBox-root .css-1ow8u3y::after, .css-1rgmi2n::after, .css-bjjbb7::after, .css-hds0vx::after,
.css-aafiep::after, .css-xd8r7u::after, .css-ya2z3b::after, .css-8sla8j::after, .css-ulr2qx::after, .css-17rlh6j::after,
.main .bg-primary-foreground::after, .side .bg-primary-foreground::after,
.main .bg-foreground::after, .side .bg-foreground::after,
.main .bg-content1::after, .side .bg-content1::after,
.main .bg-default::after, .side .bg-default::after,
.MuiButton-root::after, .MuiListItemButton-root::after {
content: '' !important;
position: absolute !important;
top: 2px; left: 2px; right: 2px; bottom: 2px; /* 避免覆盖边框 */
border-radius: inherit !important;
overflow: hidden !important;
background-image:
  /* 使用扩展的粒子颜色变量 */
  radial-gradient(var(--particle-red) 1.2px, transparent 0),
  radial-gradient(var(--particle-orange) 0.8px, transparent 0),
  radial-gradient(var(--particle-yellow) 1.5px, transparent 0),
  radial-gradient(var(--particle-green) 1px, transparent 0),
  radial-gradient(var(--particle-cyan) 1.3px, transparent 0),
  radial-gradient(var(--particle-blue) 0.9px, transparent 0),
  radial-gradient(var(--particle-purple) 1.1px, transparent 0),
  radial-gradient(var(--particle-magenta) 1.4px, transparent 0),
  radial-gradient(var(--particle-neon-pink) 1.6px, transparent 0),
  radial-gradient(var(--particle-neon-green) 0.7px, transparent 0),
  radial-gradient(var(--particle-neon-blue) 1.8px, transparent 0),
  radial-gradient(var(--particle-gold) 1.3px, transparent 0),
  radial-gradient(var(--particle-silver) 0.9px, transparent 0),
  radial-gradient(var(--particle-ruby) 1.4px, transparent 0),
  radial-gradient(var(--particle-emerald) 0.8px, transparent 0) !important;

background-size:
  47px 47px, 67px 67px, 83px 83px, 59px 59px,
  101px 101px, 131px 131px, 157px 157px, 113px 113px,
  179px 179px, 191px 191px, 227px 227px, 239px 239px,
  97px 97px, 109px 109px, 127px 127px !important;

/* 恢复原有的动画系统，增强随机弧形轨迹 */
animation:
  cyber-particles-ultra-fast 12s linear infinite,
  cyber-particles-very-fast 18s ease-in-out infinite,
  cyber-particles-fast 24s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
  cyber-particles-medium-fast 32s ease-out infinite,
  cyber-particles-medium 42s ease-in infinite,
  cyber-particles-medium-slow 54s cubic-bezier(0.55, 0.06, 0.68, 0.19) infinite,
  cyber-particles-slow 68s ease-in-out infinite,
  cyber-particles-very-slow 78s linear infinite,
  cyber-particles-ultra-slow 88s cubic-bezier(0.17, 0.67, 0.83, 0.67) infinite,
  cyber-particles-color-shift 20s ease-in-out infinite,
  cyber-particles-size-pulse 16s cubic-bezier(0.4, 0, 0.6, 1) infinite,
  cyber-particles-opacity-wave 14s ease-in-out infinite !important;
opacity: 0 !important;
transition: opacity 0.5s ease-in-out !important;
z-index: 0 !important;
pointer-events: none !important;
}

/* 激活悬停效果 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover,
.main .bg-primary-foreground:hover, .side .bg-primary-foreground:hover,
.main .bg-foreground:hover, .side .bg-foreground:hover,
.main .bg-content1:hover, .side .bg-content1:hover,
.main .bg-default:hover, .side .bg-default:hover,
.MuiButton-root:hover, .MuiListItemButton-root:hover {
transform: translateY(-4px) !important;
}

/* 悬停时卡片背景透明度变化 */
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
background-color: var(--card-bg-light-hover) !important;
}
@media (prefers-color-scheme: dark) {
.MuiBox-root .css-1ow8u3y:hover, .css-1rgmi2n:hover, .css-bjjbb7:hover, .css-hds0vx:hover,
.css-aafiep:hover, .css-xd8r7u:hover, .css-ya2z3b:hover, .css-8sla8j:hover, .css-ulr2qx:hover, .css-17rlh6j:hover {
  background-color: var(--card-bg-dark-hover) !important;
}
}

/* 悬停时激活边框并启动透明度脉冲动画 */
.MuiBox-root .css-1ow8u3y:hover::before, .css-1rgmi2n:hover::before, .css-bjjbb7:hover::before, .css-hds0vx:hover::before,
.css-aafiep:hover::before, .css-xd8r7u:hover::before, .css-ya2z3b:hover::before, .css-8sla8j:hover::before, .css-ulr2qx:hover::before, .css-17rlh6j:hover::before,
.main .bg-primary-foreground:hover::before, .side .bg-primary-foreground:hover::before,
.main .bg-foreground:hover::before, .side .bg-foreground::before,
.main .bg-content1:hover::before, .side .bg-content1::before,
.main .bg-default:hover::before, .side .bg-default::before,
.MuiButton-root:hover::before, .MuiListItemButton-root:hover::before {
opacity: 1 !important;
animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}

/* 悬停时激活粒子效果 */
.MuiBox-root .css-1ow8u3y:hover::after, .css-1rgmi2n:hover::after, .css-bjjbb7:hover::after, .css-hds0vx:hover::after,
.css-aafiep:hover::after, .css-xd8r7u:hover::after, .css-ya2z3b:hover::after, .css-8sla8j:hover::after, .css-ulr2qx:hover::after, .css-17rlh6j:hover::after,
.main .bg-primary-foreground:hover::after, .side .bg-primary-foreground:hover::after,
.main .bg-foreground:hover::after, .side .bg-foreground::after,
.main .bg-content1:hover::after, .side .bg-content1::after,
.main .bg-default:hover::after, .side .bg-default::after,
.MuiButton-root:hover::after, .MuiListItemButton-root:hover::after {
opacity: 1 !important;
}


/* 导航项目整体盒子*/
.css-dh9epo{
min-width:280px;
}

/* 导航项目样式优化 */
.MuiListItem-root .MuiListItemButton-root {
width: var(--nav-item-width) !important;
border-radius: var(--nav-item-border-radius) !important;
background-color: transparent !important;
margin: 4px 0 !important;
transition: all 0.3s ease !important;
display: flex !important;
align-items: center !important;
justify-content: center !important;
}

/* 导航项目文字水平居中 */
.MuiListItem-root .MuiListItemButton-root .MuiListItemText-root {
display: flex !important;
align-items: center !important;
}

/* ========================================
 Gemini 风格渐变色和悬停效果
 ======================================== */

/* Gemini 风格 CSS 变量定义 */
:root {
--gemini-color-logo-gradient: linear-gradient(90deg,
    #2079fe 0%,
    #098efb 33.53%,
    #ad89eb 70%,
    #ef4e5e 100%);
--gemini-color-white: #ffffff;
--gemini-color-black: #000000;
--gemini-color-grey-50: #f8f9fa;
--gemini-color-grey-100: #f1f3f4;
--gemini-color-grey-200: #e8eaed;
--gemini-color-grey-300: #dadce0;
--gemini-color-grey-400: #bdc1c6;
--gemini-color-grey-500: #9aa0a6;
--gemini-color-grey-600: #80868b;
--gemini-color-grey-700: #5f6368;
--gemini-color-grey-800: #3c4043;
--gemini-color-grey-900: #202124;
--gemini-color-blue-50: #e8f0fe;
--gemini-color-blue-100: #d2e3fc;
--gemini-color-blue-200: #aecbfa;
--gemini-color-blue-300: #8ab4f8;
--gemini-color-blue-400: #669df6;
--gemini-color-blue-500: #4285f4;
--gemini-color-blue-600: #1a73e8;
--gemini-color-blue-700: #1967d2;
--gemini-color-blue-800: #185abc;
--gemini-color-blue-900: #174ea6;
--gemini-color-red-50: #fce8e6;
--gemini-color-red-100: #fad2cf;
--gemini-color-red-200: #f6aea9;
--gemini-color-red-300: #f28b82;
--gemini-color-red-400: #ee675c;
--gemini-color-red-500: #ea4335;
--gemini-color-red-600: #d93025;
--gemini-color-red-700: #c5221f;
--gemini-color-red-800: #b31412;
--gemini-color-red-900: #a50e0e;
--gemini-color-green-50: #e6f4ea;
--gemini-color-green-100: #ceead6;
--gemini-color-green-200: #a8dab5;
--gemini-color-green-300: #81c995;
--gemini-color-green-400: #5bb974;
--gemini-color-green-500: #34a853;
--gemini-color-green-600: #137333;
--gemini-color-green-700: #0d652d;
--gemini-color-green-800: #0b5394;
--gemini-color-green-900: #0a5d00;
--gemini-color-yellow-50: #fef7e0;
--gemini-color-yellow-100: #feefc3;
--gemini-color-yellow-200: #fde047;
--gemini-color-yellow-300: #fcd34d;
--gemini-color-yellow-400: #fbbf24;
--gemini-color-yellow-500: #f59e0b;
--gemini-color-yellow-600: #d97706;
--gemini-color-yellow-700: #b45309;
--gemini-color-yellow-800: #92400e;
--gemini-color-yellow-900: #78350f;
--gemini-color-gemini-blue: #368efe;
--gemini-color-gemini-cyan: #4fabff;
--gemini-color-gemini-light-blue: #b1c5ff;
--gemini-color-blue: #368efe;
--gemini-color-purple-100: #ac87eb;
--gemini-color-red-200: #ee4d5d;
--gemini-color-green-800: #137333;
--gemini-color-blue-800: #185ABC;
--gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
--gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
--gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 67.74%, #ee4d5d 100%);
--gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
--gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
--gemini-color-foreground: var(--gemini-color-white);
--gemini-color-background: var(--gemini-color-grey-900);
--gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
--gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
--gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
--gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
--gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
--gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

/* 平滑渐变色彩 - 更自然的过渡 */
--smooth-gradient: linear-gradient(90deg,
  #669df6 0%,   /* Blue */
  #7e57c2 25%,  /* Indigo */
  #ab47bc 50%,  /* Purple */
  #ec407a 75%,  /* Pink */
  #ef5350 100%  /* Red */
);

/* 平滑流动渐变 - 更长的过渡距离 */
--smooth-flowing-gradient: linear-gradient(90deg,
  #669df6, #7e57c2, #ab47bc, #ec407a, #ef5350, #ec407a, #ab47bc, #7e57c2, #669df6
);
}


/* 左侧导航文字渐变效果 - 使用平滑渐变 */
.MuiListItemText-primary {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
left: 50px !important;
width: 100% !important;
}


/* 文字渐变流动动画 */
@keyframes text-gradient-flow {
0% {
  background-position: 0% center !important;
}
100% {
  background-position: 200% center !important;
}
}

/* SVG logo震撼渐变效果 - 多彩流动渐变 */
#layout1 {
position: relative !important;
overflow: hidden !important;
}

/* 为SVG添加动态渐变背景 */
#layout1::before {
content: '' !important;
position: absolute !important;
top: -50% !important;
left: -50% !important;
width: 200% !important;
height: 200% !important;
background: linear-gradient(
  45deg,
  #4285f4 0%,
  #ea4335 15%,
  #fbbc04 30%,
  #34a853 45%,
  #4285f4 60%,
  #ea4335 75%,
  #fbbc04 90%,
  #34a853 100%
) !important;
background-size: 400% 400% !important;
animation: logo-gradient-flow 6s ease-in-out infinite !important;
z-index: -1 !important;
border-radius: 50% !important;
filter: blur(8px) !important;
}

/* SVG路径的震撼效果 */
#layout1 .st1 {
fill: #e22acd !important; /* Original fill, will be overridden by specific nth-of-type */
}

/* New: Random single colors for Clash Verge title */
#layout1 .st1:nth-of-type(1) { fill: #00bcd4 !important; } /* Cyan */
#layout1 .st1:nth-of-type(2) { fill: #ff9800 !important; } /* Orange */
#layout1 .st1:nth-of-type(3) { fill: #4caf50 !important; } /* Green */
#layout1 .st1:nth-of-type(4) { fill: #2196f3 !important; } /* Blue */
#layout1 .st1:nth-of-type(5) { fill: #e91e63 !important; } /* Pink */
#layout1 .st1:nth-of-type(6) { fill: #ffeb3b !important; } /* Yellow */
#layout1 .st1:nth-of-type(7) { fill: #9c27b0 !important; } /* Purple */
#layout1 .st1:nth-of-type(8) { fill: #795548 !important; } /* Brown */
#layout1 .st1:nth-of-type(9) { fill: #03a9f4 !important; } /* Light Blue */
#layout1 .st1:nth-of-type(10) { fill: #8bc34a !important; } /* Light Green */


/* 震撼的渐变流动动画 */
@keyframes logo-gradient-flow {
0% {
  background-position: 0% 50% !important;
  transform: rotate(0deg) scale(1) !important;
}
25% {
  background-position: 100% 50% !important;
  transform: rotate(90deg) scale(1.1) !important;
}
50% {
  background-position: 100% 100% !important;
  transform: rotate(180deg) scale(1.2) !important;
}
75% {
  background-position: 0% 100% !important;
  transform: rotate(270deg) scale(1.1) !important;
}
100% {
  background-position: 0% 50% !important;
  transform: rotate(360deg) scale(1) !important;
}
}

/* 脉冲效果 */
@keyframes logo-pulse {
0% {
  filter: drop-shadow(0 0 8px rgba(66, 133, 244, 0.6))
          drop-shadow(0 0 16px rgba(234, 67, 53, 0.4))
          drop-shadow(0 0 24px rgba(251, 188, 4, 0.3)) !important;
}
100% {
  filter: drop-shadow(0 0 12px rgba(66, 133, 244, 0.8))
          drop-shadow(0 0 24px rgba(234, 67, 53, 0.6))
          drop-shadow(0 0 36px rgba(251, 188, 4, 0.5)) !important;
}
}

/* 颜色变换动画 */
@keyframes logo-color-shift {
0% { fill: #4285f4 !important; }
25% { fill: #ea4335 !important; }
50% { fill: #fbbc04 !important; }
75% { fill: #34a853 !important; }
100% { fill: #4285f4 !important; }
}

/* data-tauri-drag-region 元素文字渐变效果 */
.css-1l0zim6 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
}

/* 右侧设置页面文字渐变效果 - 只针对文字，不影响图标 */
.css-1i24pk4 span:not([class*="MuiSvgIcon"]):not([class*="Icon"]) {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
color: transparent !important;
font-weight: bold !important;
font-size: 20px;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
background-image: var(--smooth-gradient) !important;
background-clip: text !important;
-webkit-background-clip: text !important;
-webkit-text-fill-color: transparent !important;
font-weight: bold !important;
background-size: 200% auto !important;
animation: text-gradient-flow 8s linear infinite !important;
}

/* 赛博朋克边框旋转动画 */
@keyframes cyberpunk-border-flow {
to {
  --cyber-border-angle: 360deg;
}
}

/* 新增：赛博朋克边框透明度脉冲动画 */
@keyframes cyberpunk-border-opacity-pulse {
0%, 100% { opacity: 0.6; }
50% { opacity: 1; }
}

/* 恢复并增强的粒子动画系统 - 随机弧形轨迹，360度任意方向 */

/* 扩展粒子颜色变量 */
:root {
  /* 彩虹光谱系列 */
  --particle-red: rgba(255, 0, 0, 0.9);
  --particle-orange: rgba(255, 140, 0, 0.9);
  --particle-yellow: rgba(255, 255, 0, 0.9);
  --particle-lime: rgba(50, 205, 50, 0.9);
  --particle-green: rgba(0, 255, 0, 0.9);
  --particle-cyan: rgba(0, 255, 255, 0.9);
  --particle-blue: rgba(0, 100, 255, 0.9);
  --particle-purple: rgba(138, 43, 226, 0.9);
  --particle-magenta: rgba(255, 0, 255, 0.9);

  /* 霓虹色系列 - 超鲜艳 */
  --particle-neon-pink: rgba(255, 20, 147, 1.0);
  --particle-neon-green: rgba(57, 255, 20, 1.0);
  --particle-neon-blue: rgba(0, 191, 255, 1.0);
  --particle-neon-orange: rgba(255, 69, 0, 1.0);
  --particle-neon-purple: rgba(148, 0, 211, 1.0);
  --particle-neon-yellow: rgba(255, 255, 0, 1.0);
  --particle-neon-red: rgba(255, 0, 54, 1.0);
  --particle-neon-cyan: rgba(0, 255, 255, 1.0);

  /* 鲜艳金属色 */
  --particle-gold: rgba(255, 215, 0, 0.95);
  --particle-silver: rgba(220, 220, 220, 0.95);
  --particle-copper: rgba(255, 140, 0, 0.95);
  --particle-bronze: rgba(205, 127, 50, 0.95);

  /* 宝石色系列 - 高亮版 */
  --particle-ruby: rgba(255, 0, 100, 0.95);
  --particle-emerald: rgba(0, 255, 100, 0.95);
  --particle-sapphire: rgba(0, 100, 255, 0.95);
  --particle-diamond: rgba(200, 255, 255, 0.95);
  --particle-amethyst: rgba(200, 100, 255, 0.95);

  /* 特殊鲜艳色 */
  --particle-electric: rgba(0, 255, 255, 1.0);
  --particle-fire: rgba(255, 69, 0, 1.0);
  --particle-plasma: rgba(255, 100, 255, 1.0);
  --particle-laser: rgba(255, 0, 255, 1.0);
  --particle-atomic: rgba(0, 255, 0, 1.0);
  --particle-pink: rgba(255, 192, 203, 0.9);
  --particle-teal: rgba(0, 128, 128, 0.9);
  --particle-violet: rgba(238, 130, 238, 0.9);
  --particle-brown: rgba(165, 42, 42, 0.9);
  --particle-indigo: rgba(75, 0, 130, 0.9);
  --particle-maroon: rgba(128, 0, 0, 0.9);
  --particle-navy: rgba(0, 0, 128, 0.9);
  --particle-olive: rgba(128, 128, 0, 0.9);
  --particle-coral: rgba(255, 127, 80, 0.9);
  --particle-crimson: rgba(220, 20, 60, 0.9);

  /* 统一粒子大小 */
  --particle-size: 3px; /* All particles same size */
}

/* 超快速弧形粒子动画 - 随机360度方向 */
@keyframes cyber-particles-ultra-fast {
0% { background-position: 13px 7px, 89px 23px, 156px 41px, 234px 67px, 312px 89px, 67px 134px, 145px 178px, 223px 201px, 301px 245px, 89px 289px, 167px 312px, 245px 356px, 323px 389px, 401px 423px, 123px 456px; }
25% { background-position: -234px 456px, 567px -123px, -345px 678px, 789px -234px, -456px 567px, 678px -345px, -567px 789px, 890px -456px, -678px 567px, 789px -678px, -890px 456px, 567px -789px, -456px 890px, 678px -567px, -789px 456px; }
50% { background-position: 678px -890px, -567px 789px, 890px -456px, -678px 567px, 789px -890px, -456px 678px, 567px -789px, -890px 456px, 678px -567px, -789px 890px, 456px -678px, -567px 789px, 890px -456px, -678px 567px, 789px -890px; }
75% { background-position: -890px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px, 678px 567px, -789px -890px, 456px 678px, -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px; }
100% { background-position: -1234px 1567px, -1789px -1234px, 1567px 1890px, 1234px -1567px, -1890px 1234px, 1567px -1789px, -1234px 1890px, 1789px -1234px, -1567px 1234px, 1890px -1567px, -1234px 1789px, 1567px -1890px, -1789px 1234px, 1234px -1567px, -1890px 1789px; }
}

/* 很快速弧形粒子动画 */
@keyframes cyber-particles-very-fast {
0% { background-position: 29px 13px, 107px 37px, 185px 61px, 263px 85px, 341px 109px, 85px 153px, 163px 177px, 241px 201px, 319px 225px, 107px 269px, 185px 293px, 263px 317px, 341px 361px, 419px 385px, 141px 429px; }
25% { background-position: -345px 567px, 678px -234px, -456px 789px, 567px -345px, -678px 456px, 789px -567px, -456px 678px, 567px -789px, -890px 456px, 678px -567px, -789px 890px, 456px -678px, -567px 789px, 890px -456px, -678px 567px; }
50% { background-position: 789px -456px, -678px 567px, 890px -789px, -456px 678px, 567px -890px, -789px 456px, 678px -567px, -890px 789px, 456px -678px, -567px 890px, 789px -456px, -678px 567px, 890px -789px, -456px 678px, 567px -890px; }
75% { background-position: -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px, 567px 789px, -890px -456px, 678px 567px, -789px -890px, 456px 678px, -567px -789px, 890px 456px, -678px -567px, 789px 890px, -456px -678px; }
100% { background-position: -1456px 1789px, -1567px -1456px, 1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px; }
}

/* 快速弧形粒子动画 */
@keyframes cyber-particles-fast {
0% { background-position: 47px 19px, 125px 43px, 203px 67px, 281px 91px, 359px 115px, 103px 159px, 181px 183px, 259px 207px, 337px 231px, 125px 275px, 203px 299px, 281px 323px, 359px 347px, 437px 371px, 159px 415px; }
25% { background-position: -456px 678px, 789px -345px, -567px 890px, 678px -456px, -789px 567px, 890px -678px, -567px 789px, 678px -890px, -456px 567px, 789px -678px, -890px 456px, 567px -789px, -678px 890px, 456px -567px, -789px 678px; }
50% { background-position: 890px -567px, -789px 678px, 567px -890px, -678px 789px, 456px -567px, -890px 678px, 789px -456px, -567px 890px, 678px -789px, -456px 567px, 890px -678px, -789px 456px, 567px -890px, -678px 789px, 456px -567px; }
75% { background-position: -678px -890px, 567px 789px, -456px -567px, 890px 678px, -789px -456px, 567px 890px, -678px -789px, 456px 567px, -890px -678px, 789px 456px, -567px -890px, 678px 789px, -456px -567px, 890px 678px, -789px -456px; }
100% { background-position: -1678px 1456px, -1890px -1678px, 1456px 1890px, 1678px -1456px, -1890px 1678px, 1456px -1890px, -1678px 1456px, 1890px -1678px, -1456px 1890px, 1678px -1456px, -1890px 1678px, 1456px -1890px, -1678px 1456px, 1890px -1678px, -1456px 1890px; }
}

/* 中快速弧形粒子动画 */
@keyframes cyber-particles-medium-fast {
0% { background-position: 61px 31px, 139px 55px, 217px 79px, 295px 103px, 373px 127px, 117px 171px, 195px 195px, 273px 219px, 351px 243px, 139px 287px, 217px 311px, 295px 335px, 373px 359px, 451px 383px, 173px 427px; }
25% { background-position: -567px 789px, 890px -456px, -678px 567px, 789px -567px, -890px 678px, 567px -789px, -678px 890px, 789px -567px, -456px 678px, 890px -789px, -567px 456px, 678px -890px, -789px 567px, 456px -678px, -890px 789px; }
50% { background-position: 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px, -789px 890px, 567px -678px, -890px 789px, 678px -567px; }
75% { background-position: -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px; }
100% { background-position: -1789px 1567px, -1456px -1789px, 1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px, 1789px -1567px, -1456px 1789px, 1567px -1456px, -1789px 1567px, 1456px -1789px, -1567px 1456px; }
}

/* 中速弧形粒子动画 */
@keyframes cyber-particles-medium {
0% { background-position: 73px 41px, 151px 65px, 229px 89px, 307px 113px, 385px 137px, 129px 181px, 207px 205px, 285px 229px, 363px 253px, 151px 297px, 229px 321px, 307px 345px, 385px 369px, 463px 393px, 185px 437px; }
25% { background-position: -678px 890px, 567px -789px, -456px 678px, 890px -678px, -789px 567px, 678px -890px, -567px 789px, 890px -678px, -456px 567px, 789px -890px, -678px 456px, 567px -789px, -890px 678px, 789px -567px, -456px 890px; }
50% { background-position: 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px, -890px 567px, 678px -789px, -567px 890px, 789px -678px; }
75% { background-position: -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px, 789px 567px, -890px -678px, 567px 789px, -678px -890px; }
100% { background-position: -1567px 1890px, -1678px -1567px, 1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px; }
}

/* 中慢速弧形粒子动画 */
@keyframes cyber-particles-medium-slow {
0% { background-position: 97px 53px, 175px 77px, 253px 101px, 331px 125px, 409px 149px, 153px 193px, 231px 217px, 309px 241px, 387px 265px, 175px 309px, 253px 333px, 331px 357px, 409px 381px, 487px 405px, 209px 449px; }
25% { background-position: -789px 567px, 678px -890px, -567px 789px, 890px -789px, -678px 567px, 789px -567px, -890px 678px, 567px -789px, -678px 890px, 789px -567px, -456px 678px, 890px -789px, -567px 456px, 678px -890px, -789px 567px; }
50% { background-position: 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px, -567px 678px, 789px -890px, -678px 567px, 890px -789px; }
75% { background-position: -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px, 890px 678px, -567px -789px, 678px 890px, -789px -567px; }
100% { background-position: -1890px 1678px, -1567px -1890px, 1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px, 1890px -1678px, -1567px 1890px, 1678px -1567px, -1890px 1678px, 1567px -1890px, -1678px 1567px; }
}

/* 慢速弧形粒子动画 */
@keyframes cyber-particles-slow {
0% { background-position: 109px 67px, 187px 91px, 265px 115px, 343px 139px, 421px 163px, 165px 207px, 243px 231px, 321px 255px, 399px 279px, 187px 323px, 265px 347px, 343px 371px, 421px 395px, 499px 419px, 221px 463px; }
25% { background-position: -456px 789px, 890px -567px, -678px 456px, 567px -890px, -789px 678px, 456px -567px, -890px 789px, 678px -456px, -567px 890px, 789px -678px, -456px 567px, 890px -789px, -678px 456px, 567px -890px, -789px 678px; }
50% { background-position: 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px, -789px 890px, 456px -567px, -890px 789px, 567px -456px; }
75% { background-position: -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px, 789px 567px, -890px -456px, 567px 789px, -456px -890px; }
100% { background-position: -1456px 1789px, -1890px -1456px, 1789px 1890px, 1456px -1789px, -1890px 1456px, 1789px -1890px, -1456px 1789px, 1890px -1456px, -1789px 1890px, 1456px -1789px, -1890px 1456px, 1789px -1890px, -1456px 1789px, 1890px -1456px, -1789px 1890px; }
}

/* 很慢速弧形粒子动画 */
@keyframes cyber-particles-very-slow {
0% { background-position: 127px 79px, 205px 103px, 283px 127px, 361px 151px, 439px 175px, 183px 219px, 261px 243px, 339px 267px, 417px 291px, 205px 335px, 283px 359px, 361px 383px, 439px 407px, 517px 431px, 239px 475px; }
25% { background-position: -567px 456px, 678px -789px, -890px 567px, 456px -678px, -789px 890px, 567px -456px, -678px 789px, 890px -567px, -456px 678px, 789px -890px, -567px 456px, 678px -789px, -890px 567px, 456px -678px, -789px 890px; }
50% { background-position: 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px, -456px 789px, 567px -678px, -789px 456px, 678px -567px; }
75% { background-position: -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px, 678px 456px, -789px -567px, 456px 678px, -567px -789px; }
100% { background-position: -1678px 1567px, -1456px -1678px, 1567px 1456px, 1678px -1567px, -1456px 1678px, 1567px -1456px, -1678px 1567px, 1456px -1678px, -1567px 1456px, 1678px -1567px, -1456px 1678px, 1567px -1456px, -1678px 1567px, 1456px -1678px, -1567px 1456px; }
}

/* 超慢速弧形粒子动画 */
@keyframes cyber-particles-ultra-slow {
0% { background-position: 143px 97px, 221px 121px, 299px 145px, 377px 169px, 455px 193px, 199px 237px, 277px 261px, 355px 285px, 433px 309px, 221px 353px, 299px 377px, 377px 401px, 455px 425px, 533px 449px, 255px 493px; }
25% { background-position: -678px 345px, 456px -567px, -789px 678px, 345px -456px, -567px 789px, 678px -345px, -456px 567px, 789px -678px, -345px 456px, 567px -789px, -678px 345px, 456px -567px, -789px 678px, 345px -456px, -567px 789px; }
50% { background-position: 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px, -678px 567px, 345px -456px, -567px 678px, 456px -345px; }
75% { background-position: -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px, 456px 678px, -567px -345px, 678px 456px, -345px -567px; }
100% { background-position: -1345px 1678px, -1567px -1345px, 1678px 1567px, 1345px -1678px, -1567px 1345px, 1678px -1567px, -1345px 1678px, 1567px -1345px, -1678px 1567px, 1345px -1678px, -1567px 1345px, 1678px -1567px, -1345px 1678px, 1567px -1345px, -1678px 1567px; }
}

/* 颜色变化动画 */
@keyframes cyber-particles-color-shift {
0% { filter: hue-rotate(0deg) saturate(1) brightness(1); }
25% { filter: hue-rotate(90deg) saturate(1.2) brightness(1.1); }
50% { filter: hue-rotate(180deg) saturate(0.8) brightness(0.9); }
75% { filter: hue-rotate(270deg) saturate(1.3) brightness(1.2); }
100% { filter: hue-rotate(360deg) saturate(1) brightness(1); }
}

/* 大小脉冲动画 */
@keyframes cyber-particles-size-pulse {
0% { transform: scale(1) rotate(0deg); }
20% { transform: scale(1.1) rotate(72deg); }
40% { transform: scale(0.9) rotate(144deg); }
60% { transform: scale(1.2) rotate(216deg); }
80% { transform: scale(0.8) rotate(288deg); }
100% { transform: scale(1) rotate(360deg); }
}

/* 透明度波浪动画 */
@keyframes cyber-particles-opacity-wave {
0% { opacity: 0.3; }
15% { opacity: 0.7; }
30% { opacity: 0.4; }
45% { opacity: 0.8; }
60% { opacity: 0.5; }
75% { opacity: 0.9; }
90% { opacity: 0.6; }
100% { opacity: 0.3; }
}

h6 {
font-weight: bold !important;
background: linear-gradient(90deg,
  #00b4d8 0%,   /* 明亮蓝 */
  #90e0ef 10%,  /* 浅蓝色 */
  #ef233c 20%,  /* 鲜艳红 */
  #ffc300 30%,  /* 亮黄 */
  #06d6a0 40%,  /* 鲜绿 */
  #a72061 50%,  /* 深紫红 */
  #fca311 60%,  /* 橙黄 */
  #3f37c9 70%,  /* 深蓝紫 */
  #ff006e 80%,  /* 玫红 */
  #5aa926 90%,  /* 森林绿 */
  #00b4d8 100%  /* 回到起始蓝形成循环 */
) !important;
background-size: 400% auto !important;
-webkit-background-clip: text !important;
background-clip: text !important;
-webkit-text-fill-color: transparent !important;
animation: rainbow-flow 8s linear infinite !important;
position: relative !important;
z-index: 1 !important;
/* 恢复文字选择功能 */
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

/* 修复h6和其他文字选择问题 */
h6::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

h6::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

/* 修复 .css-1u0w142 文字选择问题 */
.css-1u0w142 {
user-select: text !important;
-webkit-user-select: text !important;
-moz-user-select: text !important;
}

.css-1u0w142::selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}

.css-1u0w142::-moz-selection {
background: rgba(0, 123, 255, 0.3) !important;
color: #ffffff !important;
}



@keyframes rainbow-flow {
0% {
  background-position: 0% center;
  filter: brightness(1) saturate(1);
}
25% {
  background-position: 100% center;
  filter: brightness(1.1) saturate(1.2);
}
50% {
  background-position: 200% center;
  filter: brightness(0.9) saturate(0.8);
}
75% {
  background-position: 300% center;
  filter: brightness(1.2) saturate(1.3);
}
100% {
  background-position: 400% center;
  filter: brightness(1) saturate(1);
}
}

/* 1) 统一缩小按钮外边距（紧凑布局） */
button,
.MuiButton-root,
.MuiButtonBase-root,
.MuiListItemButton-root {
  margin: 2px !important; /* 大幅缩小外边距 */
}

/* 2) 选中态与悬停态同效（赛博灯光 + 粒子激活 + 浮起） */
.MuiBox-root .css-1ow8u3y:hover,
.css-1rgmi2n:hover,
.css-bjjbb7:hover,
.css-hds0vx:hover,
.css-aafiep:hover,
.css-xd8r7u:hover,
.css-ya2z3b:hover,
.css-8sla8j:hover,
.css-ulr2qx:hover,
.css-17rlh6j:hover,
.MuiButton-root:hover,
.MuiListItemButton-root:hover,
/* 选中态补齐 */
.MuiListItemButton-root.Mui-selected,
.MuiListItemButton-root.active,
.MuiListItemButton-root[aria-current="page"],
.MuiListItemButton-root[aria-selected="true"] {
  transform: translateY(-4px) !important;
}

/* 2.1) 同步点亮赛博边框（::before）到选中态 */
.MuiBox-root .css-1ow8u3y:hover::before,
.css-1rgmi2n:hover::before,
.css-bjjbb7:hover::before,
.css-hds0vx:hover::before,
.css-aafiep:hover::before,
.css-xd8r7u:hover::before,
.css-ya2z3b:hover::before,
.css-8sla8j:hover::before,
.css-ulr2qx:hover::before,
.css-17rlh6j:hover::before,
.MuiButton-root:hover::before,
.MuiListItemButton-root:hover::before,
/* 选中态补齐 */
.MuiListItemButton-root.Mui-selected::before,
.MuiListItemButton-root.active::before,
.MuiListItemButton-root[aria-current="page"]::before,
.MuiListItemButton-root[aria-selected="true"]::before {
  opacity: 1 !important;
  animation: cyberpunk-border-flow 4s linear infinite, cyberpunk-border-opacity-pulse 2s ease-in-out infinite !important;
}

/* ========== 3) 粒子引擎：重构 ::after ========== */
/* 覆盖旧的 ::after 粒子段（保持选择器集合一致），改为"独立粒子"实现 */
.MuiBox-root .css-1ow8u3y::after, .css-1rgmi2n::after, .css-bjjbb7::after, .css-hds0vx::after,
.css-aafiep::after, .css-xd8r7u::after, .css-ya2z3b::after, .css-8sla8j::after, .css-ulr2qx::after, .css-17rlh6j::after,
.main .bg-primary-foreground::after, .side .bg-primary-foreground::after,
.main .bg-foreground::after, .side .bg-foreground::after,
.main .bg-content1::after, .side .bg-content1::after,
.main .bg-default::after, .side .bg-default::after,
.MuiButton-root::after, .MuiListItemButton-root::after {
  content: '' !important;
  position: absolute !important;
  inset: 2px !important;            /* 不覆盖边框 */
  border-radius: inherit !important;
  overflow: hidden !important;      /* 粒子可越界，但在容器外不可见 */
  pointer-events: none !important;
  z-index: 0 !important;
  border-radius: 50% !important; /* 确保粒子为圆形 */
  /* 24 粒子：每个 radial-gradient 一粒子，独立颜色与尺寸 */
  background-image:
    /* Using a wider variety of existing colors, all with unified size */
    radial-gradient(var(--cyber-p1) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p5) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p10) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p15) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p20) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p25) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p30) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p35) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p40) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p45) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p50) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p55) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p60) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p65) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p70) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p75) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p80) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p85) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p90) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p95) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--cyber-p100) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--particle-neon-pink) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--particle-neon-green) var(--particle-size), transparent var(--particle-size)),
    radial-gradient(var(--particle-neon-blue) var(--particle-size), transparent var(--particle-size))
  !important;

  background-repeat: no-repeat !important;
  /* All particles same size */
  background-size: var(--particle-size) var(--particle-size) !important;

  /* 每个粒子的独立位置变量（百分比），初始值分散 */
  background-position:
    var(--p1x)  var(--p1y),
    var(--p2x)  var(--p2y),
    var(--p3x)  var(--p3y),
    var(--p4x)  var(--p4y),
    var(--p5x)  var(--p5y),
    var(--p6x)  var(--p6y),
    var(--p7x)  var(--p7y),
    var(--p8x)  var(--p8y),
    var(--p9x)  var(--p9y),
    var(--p10x) var(--p10y),
    var(--p11x) var(--p11y),
    var(--p12x) var(--p12y),
    var(--p13x) var(--p13y),
    var(--p14x) var(--p14y),
    var(--p15x) var(--p15y),
    var(--p16x) var(--p16y),
    var(--p17x) var(--p17y),
    var(--p18x) var(--p18y),
    var(--p19x) var(--p19y),
    var(--p20x) var(--p20y),
    var(--p21x) var(--p21y),
    var(--p22x) var(--p22y),
    var(--p23x) var(--p23y),
    var(--p24x) var(--p24y)
  !important;

  /* 默认隐藏，悬停或选中时显现（见下方选择器） */
  opacity: 0 !important;
  transition: opacity 0.5s ease-in-out !important;

  /* 为每个粒子启动各自动画（不同行为/节奏/延迟/方向/缓动） */
  /* Wider speed range (10s to 100s) and varied delays */
  animation:
    move-p1  30s linear infinite -5s,
    move-p2  22s ease-in-out infinite,
    move-p3  40s cubic-bezier(.25,.46,.45,.94) infinite -10s,
    move-p4  28s ease-in infinite alternate,
    move-p5  35s ease-out infinite -15s,
    move-p6  45s linear infinite reverse,
    move-p7  25s ease-in-out infinite -7s,
    move-p8  50s cubic-bezier(.55,.06,.68,.19) infinite alternate-reverse,
    move-p9  38s ease-in infinite,
    move-p10 26s ease-out infinite -12s,
    move-p11 42s linear infinite,
    move-p12 33s ease-in-out infinite -6s,
    move-p13 36s cubic-bezier(.17,.67,.83,.67) infinite,
    move-p14 48s ease-in infinite -9s,
    move-p15 29s ease-out infinite reverse,
    move-p16 39s linear infinite -11s,
    move-p17 32s ease-in-out infinite,
    move-p18 31s cubic-bezier(.4,0,.6,1) infinite alternate,
    move-p19 43s ease-in infinite -13s,
    move-p20 20s ease-out infinite,
    move-p21 55s linear infinite alternate-reverse,
    move-p22 37s ease-in-out infinite -14s,
    move-p23 27s cubic-bezier(.25,.46,.45,.94) infinite,
    move-p24 60s linear infinite -16s
  !important;
}

/* 悬停与选中时显示粒子 */
.MuiBox-root .css-1ow8u3y:hover::after,
.css-1rgmi2n:hover::after,
.css-bjjbb7:hover::after,
.css-hds0vx:hover::after,
.css-aafiep:hover::after,
.css-xd8r7u:hover::after,
.css-ya2z3b:hover::after,
.css-8sla8j:hover::after,
.css-ulr2qx:hover::after,
.css-17rlh6j:hover::after,
.MuiButton-root:hover::after,
.MuiListItemButton-root:hover::after,
/* 选中态补齐 */
.MuiListItemButton-root.Mui-selected::after,
.MuiListItemButton-root.active::after,
.MuiListItemButton-root[aria-current="page"]::after,
.MuiListItemButton-root[aria-selected="true"]::after {
  opacity: 1 !important;
}

/* 3.1) 注册 24 对位置变量（百分比） */
@property --p1x { syntax: '<percentage>'; inherits: false; initial-value: 10%; }
@property --p1y { syntax: '<percentage>'; inherits: false; initial-value: 20%; }
@property --p2x { syntax: '<percentage>'; inherits: false; initial-value: 80%; }
@property --p2y { syntax: '<percentage>'; inherits: false; initial-value: 15%; }
@property --p3x { syntax: '<percentage>'; inherits: false; initial-value: 30%; }
@property --p3y { syntax: '<percentage>'; inherits: false; initial-value: 70%; }
@property --p4x { syntax: '<percentage>'; inherits: false; initial-value: 55%; }
@property --p4y { syntax: '<percentage>'; inherits: false; initial-value: 5%; }
@property --p5x { syntax: '<percentage>'; inherits: false; initial-value: 5%; }
@property --p5y { syntax: '<percentage>'; inherits: false; initial-value: 45%; }
@property --p6x { syntax: '<percentage>'; inherits: false; initial-value: 65%; }
@property --p6y { syntax: '<percentage>'; inherits: false; initial-value: 85%; }
@property --p7x { syntax: '<percentage>'; inherits: false; initial-value: 25%; }
@property --p7y { syntax: '<percentage>'; inherits: false; initial-value: 35%; }
@property --p8x { syntax: '<percentage>'; inherits: false; initial-value: 95%; }
@property --p8y { syntax: '<percentage>'; inherits: false; initial-value: 55%; }
@property --p9x { syntax: '<percentage>'; inherits: false; initial-value: 45%; }
@property --p9y { syntax: '<percentage>'; inherits: false; initial-value: 95%; }
@property --p10x { syntax: '<percentage>'; inherits: false; initial-value: 15%; }
@property --p10y { syntax: '<percentage>'; inherits: false; initial-value: 85%; }
@property --p11x { syntax: '<percentage>'; inherits: false; initial-value: 75%; }
@property --p11y { syntax: '<percentage>'; inherits: false; initial-value: 25%; }
@property --p12x { syntax: '<percentage>'; inherits: false; initial-value: 35%; }
@property --p12y { syntax: '<percentage>'; inherits: false; initial-value: 15%; }
@property --p13x { syntax: '<percentage>'; inherits: false; initial-value: 60%; }
@property --p13y { syntax: '<percentage>'; inherits: false; initial-value: 40%; }
@property --p14x { syntax: '<percentage>'; inherits: false; initial-value: 20%; }
@property --p14y { syntax: '<percentage>'; inherits: false; initial-value: 60%; }
@property --p15x { syntax: '<percentage>'; inherits: false; initial-value: 40%; }
@property --p15y { syntax: '<percentage>'; inherits: false; initial-value: 10%; }
@property --p16x { syntax: '<percentage>'; inherits: false; initial-value: 90%; }
@property --p16y { syntax: '<percentage>'; inherits: false; initial-value: 40%; }
@property --p17x { syntax: '<percentage>'; inherits: false; initial-value: 10%; }
@property --p17y { syntax: '<percentage>'; inherits: false; initial-value: 90%; }
@property --p18x { syntax: '<percentage>'; inherits: false; initial-value: 70%; }
@property --p18y { syntax: '<percentage>'; inherits: false; initial-value: 70%; }
@property --p19x { syntax: '<percentage>'; inherits: false; initial-value: 50%; }
@property --p19y { syntax: '<percentage>'; inherits: false; initial-value: 30%; }
@property --p20x { syntax: '<percentage>'; inherits: false; initial-value: 30%; }
@property --p20y { syntax: '<percentage>'; inherits: false; initial-value: 50%; }
@property --p21x { syntax: '<percentage>'; inherits: false; initial-value: 80%; }
@property --p21y { syntax: '<percentage>'; inherits: false; initial-value: 80%; }
@property --p22x { syntax: '<percentage>'; inherits: false; initial-value: 60%; }
@property --p22y { syntax: '<percentage>'; inherits: false; initial-value: 20%; }
@property --p23x { syntax: '<percentage>'; inherits: false; initial-value: 20%; }
@property --p23y { syntax: '<percentage>'; inherits: false; initial-value: 20%; }
@property --p24x { syntax: '<percentage>'; inherits: false; initial-value: 75%; }
@property --p24y { syntax: '<percentage>'; inherits: false; initial-value: 5%; }

/* 3.2) 为每一粒子定义独立运动轨迹（穿越 -30%~130% 视口，形成"漫游"） */
/* Enhanced keyframes for more curved/elliptical trajectories and wider range */
@keyframes move-p1  { 0%{--p1x:-20%;--p1y:10%;} 20%{--p1x:30%;--p1y:110%;} 40%{--p1x:90%;--p1y:10%;} 60%{--p1x:130%;--p1y:60%;} 80%{--p1x:60%;--p1y:-20%;} 100%{--p1x:-20%;--p1y:10%;} }
@keyframes move-p2  { 0%{--p2x:120%;--p2y:20%;} 20%{--p2x:60%;--p2y:130%;} 40%{--p2x:-10%;--p2y:70%;} 60%{--p2x:80%;--p2y:-20%;} 80%{--p2x:130%;--p2y:80%;} 100%{--p2x:120%;--p2y:20%;} }
@keyframes move-p3  { 0%{--p3x:30%;--p3y:-20%;} 20%{--p3x:100%;--p3y:40%;} 40%{--p3x:10%;--p3y:120%;} 60%{--p3x:-20%;--p3y:60%;} 80%{--p3x:70%;--p3y:-10%;} 100%{--p3x:30%;--p3y:-20%;} }
@keyframes move-p4  { 0%{--p4x:70%;--p4y:130%;} 20%{--p4x:10%;--p4y:40%;} 40%{--p4x:120%;--p4y:-10%;} 60%{--p4x:50%;--p4y:110%;} 80%{--p4x:-20%;--p4y:80%;} 100%{--p4x:70%;--p4y:130%;} }
@keyframes move-p5  { 0%{--p5x:-10%;--p5y:50%;} 20%{--p5x:80%;--p5y:130%;} 40%{--p5x:130%;--p5y:30%;} 60%{--p5x:40%;--p5y:-20%;} 80%{--p5x:-20%;--p5y:90%;} 100%{--p5x:-10%;--p5y:50%;} }
@keyframes move-p6  { 0%{--p6x:130%;--p6y:90%;} 20%{--p6x:50%;--p6y:-10%;} 40%{--p6x:-20%;--p6y:60%;} 60%{--p6x:110%;--p6y:120%;} 80%{--p6x:20%;--p6y:10%;} 100%{--p6x:130%;--p6y:90%;} }
@keyframes move-p7  { 0%{--p7x:20%;--p7y:100%;} 20%{--p7x:100%;--p7y:50%;} 40%{--p7x:40%;--p7y:-20%;} 60%{--p7x:-10%;--p7y:120%;} 80%{--p7x:70%;--p7y:10%;} 100%{--p7x:20%;--p7y:100%;} }
@keyframes move-p8  { 0%{--p8x:90%;--p8y:-10%;} 20%{--p8x:10%;--p8y:130%;} 40%{--p8x:120%;--p8y:60%;} 60%{--p8x:30%;--p8y:-20%;} 80%{--p8x:100%;--p8y:100%;} 100%{--p8x:90%;--p8y:-10%;} }
@keyframes move-p9  { 0%{--p9x:50%;--p9y:120%;} 20%{--p9x:-10%;--p9y:70%;} 40%{--p9x:100%;--p9y:10%;} 60%{--p9x:20%;--p9y:130%;} 80%{--p9x:80%;--p9y:-20%;} 100%{--p9x:50%;--p9y:120%;} }
@keyframes move-p10 { 0%{--p10x:-10%;--p10y:90%;} 20%{--p10x:110%;--p10y:30%;} 40%{--p10x:20%;--p10y:-20%;} 60%{--p10x:130%;--p10y:100%;} 80%{--p10x:50%;--p10y:10%;} 100%{--p10x:-10%;--p10y:90%;} }
@keyframes move-p11 { 0%{--p11x:100%;--p11y:20%;} 20%{--p11x:30%;--p11y:130%;} 40%{--p11x:-20%;--p11y:60%;} 60%{--p11x:110%;--p11y:-10%;} 80%{--p11x:70%;--p11y:80%;} 100%{--p11x:100%;--p11y:20%;} }
@keyframes move-p12 { 0%{--p12x:40%;--p12y:-10%;} 20%{--p12x:130%;--p12y:90%;} 40%{--p12x:-10%;--p12y:30%;} 60%{--p12x:100%;--p12y:120%;} 80%{--p12x:50%;--p12y:0%;} 100%{--p12x:40%;--p12y:-10%;} }
@keyframes move-p13 { 0%{--p13x:70%;--p13y:40%;} 20%{--p13x:10%;--p13y:120%;} 40%{--p13x:130%;--p13y:10%;} 60%{--p13x:-10%;--p13y:80%;} 80%{--p13x:60%;--p13y:30%;} 100%{--p13x:70%;--p13y:40%;} }
@keyframes move-p14 { 0%{--p14x:20%;--p14y:70%;} 20%{--p14x:120%;--p14y:50%;} 40%{--p14x:30%;--p14y:-20%;} 60%{--p14x:100%;--p14y:130%;} 80%{--p14x:10%;--p14y:60%;} 100%{--p14x:20%;--p14y:70%;} }
@keyframes move-p15 { 0%{--p15x:50%;--p15y:20%;} 20%{--p15x:10%;--p15y:100%;} 40%{--p15x:130%;--p15y:60%;} 60%{--p15x:20%;--p15y:-10%;} 80%{--p15x:40%;--p15y:80%;} 100%{--p15x:50%;--p15y:20%;} }
@keyframes move-p16 { 0%{--p16x:110%;--p16y:50%;} 20%{--p16x:-20%;--p16y:20%;} 40%{--p16x:70%;--p16y:130%;} 60%{--p16x:30%;--p16y:10%;} 80%{--p16x:120%;--p16y:90%;} 100%{--p16x:110%;--p16y:50%;} }
@keyframes move-p17 { 0%{--p17x:-10%;--p17y:100%;} 20%{--p17x:90%;--p17y:10%;} 40%{--p17x:20%;--p17y:130%;} 60%{--p17x:120%;--p17y:40%;} 80%{--p17x:50%;--p17y:80%;} 100%{--p17x:-10%;--p17y:100%;} }
@keyframes move-p18 { 0%{--p18x:80%;--p18y:-20%;} 20%{--p18x:10%;--p18y:80%;} 40%{--p18x:130%;--p18y:50%;} 60%{--p18x:0%;--p18y:120%;} 80%{--p18x:70%;--p18y:30%;} 100%{--p18x:80%;--p18y:-20%;} }
@keyframes move-p19 { 0%{--p19x:60%;--p19y:40%;} 20%{--p19x:100%;--p19y:130%;} 40%{--p19x:-20%;--p19y:60%;} 60%{--p19x:120%;--p19y:-10%;} 80%{--p19x:30%;--p19y:90%;} 100%{--p19x:60%;--p19y:40%;} }
@keyframes move-p20 { 0%{--p20x:40%;--p20y:60%;} 20%{--p20x:130%;--p20y:20%;} 40%{--p20x:10%;--p20y:120%;} 60%{--p20x:90%;--p20y:0%;} 80%{--p20x:50%;--p20y:70%;} 100%{--p20x:40%;--p20y:60%;} }
@keyframes move-p21 { 0%{--p21x:90%;--p21y:90%;} 20%{--p21x:20%;--p21y:-10%;} 40%{--p21x:130%;--p21y:70%;} 60%{--p21x:-20%;--p21y:50%;} 80%{--p21x:70%;--p21y:130%;} 100%{--p21x:90%;--p21y:90%;} }
@keyframes move-p22 { 0%{--p22x:70%;--p22y:30%;} 20%{--p22x:-10%;--p22y:100%;} 40%{--p22x:120%;--p22y:-10%;} 60%{--p22x:30%;--p22y:130%;} 80%{--p22x:100%;--p22y:50%;} 100%{--p22x:70%;--p22y:30%;} }
@keyframes move-p23 { 0%{--p23x:30%;--p23y:30%;} 20%{--p23x:130%;--p23y:90%;} 40%{--p23x:10%;--p23y:50%;} 60%{--p23x:100%;--p23y:-10%;} 80%{--p23x:40%;--p23y:120%;} 100%{--p23x:30%;--p23y:30%;} }
@keyframes move-p24 { 0%{--p24x:80%;--p24y:10%;} 20%{--p24x:-20%;--p24y:70%;} 40%{--p24x:120%;--p24y:130%;} 60%{--p24x:50%;--p24y:-10%;} 80%{--p24x:100%;--p24y:90%;} 100%{--p24x:80%;--p24y:10%;} }

/* 优化：已选中卡片的背景颜色和粒子效果 */

/* 将已选中的单个卡片的背景颜色改成完全透明 */
.MuiListItemButton-root.Mui-selected,
.MuiListItemButton-root.active,
.MuiListItemButton-root[aria-current="page"],
.MuiListItemButton-root[aria-selected="true"] {
  background-color: transparent !important;
}

/* 移除已选中卡片的粒子效果，只保留赛博灯光效果 */
.MuiListItemButton-root.Mui-selected::after,
.MuiListItemButton-root.active::after,
.MuiListItemButton-root[aria-current="page"]::after,
.MuiListItemButton-root[aria-selected="true"]::after {
  opacity: 0 !important;
  animation: none !important; /* 确保粒子动画也停止 */
}